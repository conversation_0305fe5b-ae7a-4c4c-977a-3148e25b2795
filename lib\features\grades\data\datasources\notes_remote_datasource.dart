import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart'; // Import material for debugPrint

import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../models/notes_evaluation_model.dart';

/// Abstract interface for notes remote data source
abstract class NotesRemoteDataSource {
  /// Get notes evaluations from API
  Future<List<NotesEvaluationModel>> getNotesEvaluations({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}

/// Implementation of NotesRemoteDataSource
class NotesRemoteDataSourceImpl implements NotesRemoteDataSource {
  final ApiClient apiClient;

  NotesRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<NotesEvaluationModel>> getNotesEvaluations({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur for PAR profile if provided
      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      // Make HTTP GET request to the notesEvaluations endpoint
      final response = await apiClient.getWithToken(
        ApiEndpoints.notesEvaluations,
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes)
      );


       final decodedResponse = latin1.decode(response.data);
       final jsonResponse = jsonDecode(decodedResponse);

      // Debug print the raw response data
      debugPrint('NotesRemoteDataSourceImpl: getNotesEvaluations raw response data: $jsonResponse');

      // Parse response data
      if (jsonResponse is List) {
        return jsonResponse
            .map((json) => NotesEvaluationModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Invalid response format: expected List but got ${response.data.runtimeType}');
      }
    } catch (e) {
      // Re-throw the exception after logging
      debugPrint('NotesRemoteDataSourceImpl: Failed to get notes evaluations: $e');
      rethrow;
    }
  }
}
