import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/absence_retard_entity.dart';
import '../repositories/absences_retards_repository.dart';

/// Parameters for GetAbsencesRetardsUseCase
class GetAbsencesRetardsParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  const GetAbsencesRetardsParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });
}

/// Use case for getting absences and tardiness records
class GetAbsencesRetardsUseCase implements UseCase<List<AbsenceRetardEntity>, GetAbsencesRetardsParams> {
  final AbsencesRetardsRepository repository;

  const GetAbsencesRetardsUseCase(this.repository);

  @override
  Future<Either<Failure, List<AbsenceRetardEntity>>> call(GetAbsencesRetardsParams params) async {
    return await repository.getAbsencesRetards(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}
