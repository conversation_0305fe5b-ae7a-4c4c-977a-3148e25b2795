﻿import 'package:equatable/equatable.dart';
import '../../domain/entities/finances_response_entity.dart';
import '../../domain/entities/finances_unpaid_response_entity.dart';

/// Base finances state
abstract class FinancesState extends Equatable {
  const FinancesState();
  
  @override
  List<Object?> get props => [];
}

/// Initial finances state
class FinancesInitial extends FinancesState {
  const FinancesInitial();
}

/// Loading state during finances operations
class FinancesLoading extends FinancesState {
  const FinancesLoading();
}

/// Finances data loaded successfully
class FinancesLoaded extends FinancesState {
  final FinancesResponseEntity? paidFeesResponse;
  final FinancesUnpaidResponseEntity? unpaidFeesResponse;

  const FinancesLoaded({
    this.paidFeesResponse,
    this.unpaidFeesResponse,
  });

  @override
  List<Object?> get props => [paidFeesResponse, unpaidFeesResponse];
}

/// Finances error occurred
class FinancesError extends FinancesState {
  final String message;
  
  const FinancesError(this.message);
  
  @override
  List<Object?> get props => [message];
}
