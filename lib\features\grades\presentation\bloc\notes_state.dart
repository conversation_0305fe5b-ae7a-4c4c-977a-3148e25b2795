import 'package:equatable/equatable.dart';
import '../../domain/entities/notes_evaluation_entity.dart';

/// Base notes state
abstract class NotesState extends Equatable {
  const NotesState();
  
  @override
  List<Object?> get props => [];
}

/// Initial notes state
class NotesInitial extends NotesState {
  const NotesInitial();
}

/// Loading state during notes operations
class NotesLoading extends NotesState {
  const NotesLoading();
}

/// Notes data loaded successfully
class NotesLoaded extends NotesState {
  final List<NotesEvaluationEntity> notesEvaluations;
  
  const NotesLoaded({required this.notesEvaluations});
  
  @override
  List<Object?> get props => [notesEvaluations];
}

/// Notes error occurred
class NotesError extends NotesState {
  final String message;
  
  const NotesError(this.message);
  
  @override
  List<Object?> get props => [message];
}
