import '../../domain/entities/enfant_tuteur_entity.dart';

class EnfantTuteurModel {
  final String codeEtudiant;
  final String prenom;
  final String nom;
  final String photo; // base64 string
  final String classeInscrite;
  final String dateNaissance;

  EnfantTuteurModel({
    required this.codeEtudiant,
    required this.prenom,
    required this.nom,
    required this.photo,
    required this.classeInscrite,
    required this.dateNaissance,
  });

  factory EnfantTuteurModel.fromJson(Map<String, dynamic> json) {
    return EnfantTuteurModel(
      codeEtudiant: json['codeEtudiant'] ?? '',
      prenom: json['prenom'] ?? '',
      nom: json['nom'] ?? '',
      photo: json['photo'] ?? '',
      classeInscrite: json['classeInscrite'] ?? '',
      dateNaissance: json['dateNaissance'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'codeEtudiant': codeEtudiant,
      'prenom': prenom,
      'nom': nom,
      'photo': photo,
      'classeInscrite': classeInscrite,
      'dateNaissance': dateNaissance,
    };
  }

  EnfantTuteurEntity toEntity() {
    return EnfantTuteurEntity(
      codeEtudiant: codeEtudiant,
      prenom: prenom,
      nom: nom,
      photo: photo,
      classeInscrite: classeInscrite,
    );
  }
}

// Keep the old StudentRecord class for backward compatibility during transition
class StudentRecord {
  final String id;
  final String name;
  final String class_;
  final String birthdate;

  StudentRecord({
    required this.id,
    required this.name,
    required this.class_,
    required this.birthdate,
  });
}
