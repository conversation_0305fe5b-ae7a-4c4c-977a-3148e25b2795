import '../../domain/entities/dashboard_entity.dart';

/// Data model for dashboard information
class DashboardModel {
  final String anneeScolaire;
  final NoteModel note;
  final AssiduitModel assiduite;
  final EtatFinancierModel etatFinancier;
  final DossierModel dossier;
  final RessourcesPedagogiquesModel ressourcesPedagogiques;
  final PlanningsModel plannings;
  final CahierTexteModel cahierTexte;

  DashboardModel({
    required this.anneeScolaire,
    required this.note,
    required this.assiduite,
    required this.etatFinancier,
    required this.dossier,
    required this.ressourcesPedagogiques,
    required this.plannings,
    required this.cahierTexte,
  });

  factory DashboardModel.fromJson(Map<String, dynamic> json) {
    return DashboardModel(
      anneeScolaire: json['anneeScolaire'] ?? '',
      note: NoteModel.fromJson(json['note'] ?? {}),
      assiduite: AssiduitModel.fromJson(json['assiduite'] ?? {}),
      etatFinancier: EtatFinancierModel.fromJson(json['etatFinancier'] ?? {}),
      dossier: DossierModel.fromJson(json['dossier'] ?? {}),
      ressourcesPedagogiques: RessourcesPedagogiquesModel.fromJson(json['ressourcesPedagogiques'] ?? {}),
      plannings: PlanningsModel.fromJson(json['plannings'] ?? {}),
      cahierTexte: CahierTexteModel.fromJson(json['cahierTexte'] ?? {}),
    );
  }

  /// Convert model to entity
  DashboardEntity toEntity() {
    return DashboardEntity(
      anneeScolaire: anneeScolaire,
      note: note.toEntity(),
      assiduite: assiduite.toEntity(),
      etatFinancier: etatFinancier.toEntity(),
      dossier: dossier.toEntity(),
      ressourcesPedagogiques: ressourcesPedagogiques.toEntity(),
      plannings: plannings.toEntity(),
      cahierTexte: cahierTexte.toEntity(),
    );
  }
}

/// Data model for note information
class NoteModel {
  final int nombreNote;

  NoteModel({
    required this.nombreNote,
  });

  factory NoteModel.fromJson(Map<String, dynamic> json) {
    return NoteModel(
      nombreNote: json['nombreNote'] ?? 0,
    );
  }

  NoteEntity toEntity() {
    return NoteEntity(nombreNote: nombreNote);
  }
}

/// Data model for assiduite information
class AssiduitModel {
  final int nombreAbsence;
  final int nombreRetard;

  AssiduitModel({
    required this.nombreAbsence,
    required this.nombreRetard,
  });

  factory AssiduitModel.fromJson(Map<String, dynamic> json) {
    return AssiduitModel(
      nombreAbsence: json['nombreAbsence'] ?? 0,
      nombreRetard: json['nombreRetard'] ?? 0,
    );
  }

  AssiduiteEntity toEntity() {
    return AssiduiteEntity(
      nombreAbsence: nombreAbsence,
      nombreRetard: nombreRetard,
    );
  }
}

/// Data model for financial status information
class EtatFinancierModel {
  final double montantEncaisse;
  final double montantAencaisser;
  final double montantImpaye;

  EtatFinancierModel({
    required this.montantEncaisse,
    required this.montantAencaisser,
    required this.montantImpaye,
  });

  factory EtatFinancierModel.fromJson(Map<String, dynamic> json) {
    return EtatFinancierModel(
      montantEncaisse: (json['montantEncaisse'] ?? 0).toDouble(),
      montantAencaisser: (json['montantAencaisser'] ?? 0).toDouble(),
      montantImpaye: (json['montantImpaye'] ?? 0).toDouble(),
    );
  }

  EtatFinancierEntity toEntity() {
    return EtatFinancierEntity(
      montantEncaisse: montantEncaisse,
      montantAencaisser: montantAencaisser,
      montantImpaye: montantImpaye,
    );
  }
}

/// Data model for dossier information
class DossierModel {
  final int nombreElement;

  DossierModel({
    required this.nombreElement,
  });

  factory DossierModel.fromJson(Map<String, dynamic> json) {
    return DossierModel(
      nombreElement: json['nombreElement'] ?? 0,
    );
  }

  DossierEntity toEntity() {
    return DossierEntity(nombreElement: nombreElement);
  }
}

/// Data model for educational resources information
class RessourcesPedagogiquesModel {
  final int nombreRessource;

  RessourcesPedagogiquesModel({
    required this.nombreRessource,
  });

  factory RessourcesPedagogiquesModel.fromJson(Map<String, dynamic> json) {
    return RessourcesPedagogiquesModel(
      nombreRessource: json['nombreRessource'] ?? 0,
    );
  }

  RessourcesPedagogiquesEntity toEntity() {
    return RessourcesPedagogiquesEntity(nombreRessource: nombreRessource);
  }
}

/// Data model for planning information
class PlanningsModel {
  final int nombrePlanningSemaine;
  final int nombrePlanningJour;

  PlanningsModel({
    required this.nombrePlanningSemaine,
    required this.nombrePlanningJour,
  });

  factory PlanningsModel.fromJson(Map<String, dynamic> json) {
    return PlanningsModel(
      nombrePlanningSemaine: json['nombrePlanningSemaine'] ?? 0,
      nombrePlanningJour: json['nombrePlanningJour'] ?? 0,
    );
  }

  PlanningsEntity toEntity() {
    return PlanningsEntity(
      nombrePlanningSemaine: nombrePlanningSemaine,
      nombrePlanningJour: nombrePlanningJour,
    );
  }
}

/// Data model for cahier texte information
class CahierTexteModel {
  final int nombreEntre;
  final String semestre;

  CahierTexteModel({
    required this.nombreEntre,
    required this.semestre,
  });

  factory CahierTexteModel.fromJson(Map<String, dynamic> json) {
    return CahierTexteModel(
      nombreEntre: json['nombreEntre'] ?? 0,
      semestre: json['semestre'] ?? '',
    );
  }

  CahierTexteEntity toEntity() {
    return CahierTexteEntity(
      nombreEntre: nombreEntre,
      semestre: semestre,
    );
  }
}
