import 'package:flutter/material.dart';

class EmptyMessage extends StatelessWidget {
  final String message;
  const EmptyMessage({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(Icons.folder_off_outlined, size: 38.0, color: Colors.blueGrey),
        Text(
          message,
          style: TextStyle(fontSize: 18.0, color: Colors.grey[600]),
        ),
      ],
    );
  }
}