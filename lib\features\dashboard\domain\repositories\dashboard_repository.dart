import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/dashboard_entity.dart';

/// Abstract repository interface for dashboard operations
abstract class DashboardRepository {
  /// Load dashboard data for a student
  Future<Either<Failure, DashboardEntity>> loadDashboardData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}
