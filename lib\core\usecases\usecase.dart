import 'package:dartz/dartz.dart';
import '../error/failures.dart';

/// Abstract class for use cases.
/// A use case represents a single task that an application performs.
/// It takes a parameter [P] and returns a [Future] of [Either] a [Failure] or a result [R].
abstract class UseCase<R, P> {
  Future<Either<Failure, R>> call(P params);
}

/// Abstract class for use cases with no parameters.
abstract class NoParamsUseCase<R> {
  Future<Either<Failure, R>> call();
}

/// Represents no parameters for a use case.
class NoParams {}