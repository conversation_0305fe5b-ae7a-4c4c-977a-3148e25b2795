// Path: lib/features/schools/domain/usecases/get_user_schools_usecase.dart

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/etablissement_utilisateur.dart';
import '../repositories/schools_repository.dart';

/// Use case for fetching user-specific schools.
///
/// This use case abstracts the process of retrieving a list of schools
/// associated with the current user from the [SchoolsRepository].
class GetUserSchoolsUseCase {
  final SchoolsRepository repository;

  /// Constructs a [GetUserSchoolsUseCase] with the required [SchoolsRepository].
  const GetUserSchoolsUseCase(this.repository);

  /// Executes the use case to fetch user schools.
  ///
  /// Returns a [Future] of [Either] containing a [Failure] on the left
  /// if the operation fails, or a [List] of [EtablissementUtilisateur]
  /// on the right if successful.
  Future<Either<Failure, List<EtablissementUtilisateur>>> call(String phoneNumber) async {
    // Call the appropriate method on the repository to get user schools.
    // Assuming the repository has a method like getUserSchools().
    return await repository.getUserSchools(phoneNumber);
  }
}