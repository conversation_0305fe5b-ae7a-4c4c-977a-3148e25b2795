import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/enfant_tuteur_entity.dart';

/// Repository interface for student records operations
abstract class StudentRecordsRepository {
  /// Get children/students for a tutor/parent
  Future<Either<Failure, List<EnfantTuteurEntity>>> getEnfantsDuTuteur({
    required String codeUtilisateur,
    required String codeEtab,
    required String telephone,
  });
}
