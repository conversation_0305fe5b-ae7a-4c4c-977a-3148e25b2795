import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import '../../../domain/usecases/send_sms_usecase.dart';
import '../../../domain/usecases/resend_sms_usecase.dart';
import '../../../domain/usecases/check_response_usecase.dart'; // Import the new use case
import '../state/auth_state.dart';

/// Cubit for managing phone authentication state
class PhoneAuthenticationCubit extends Cubit<AuthState> {
  final SendSmsUseCase sendSmsUseCase;
  final ResendSmsUseCase resendSmsUseCase;
  final CheckResponseUseCase checkResponseUseCase; // Add the new use case

  PhoneAuthenticationCubit({
    required this.sendSmsUseCase,
    required this.resendSmsUseCase,
    required this.checkResponseUseCase, // Add to the constructor
  }) : super(const AuthInitial());
  
  /// Send SMS verification code to the provided phone number
  ///
  /// [phoneNumber] - The complete phone number including country code
  Future<void> sendSms(String phoneNumber) async {
    try {
      emit(const AuthSmsSending());

      debugPrint('PhoneAuthenticationCubit: Sending SMS to $phoneNumber');

      // Call the use case to send SMS (device info is handled in the data source)
      final result = await sendSmsUseCase(phoneNumber);

      result.fold(
        (failure) {
          debugPrint('PhoneAuthenticationCubit: SMS sending failed: ${failure.message}');
          emit(AuthSmsError(failure.message, phoneNumber: phoneNumber));
        },
        (success) {
          // SMS sent successfully
          // success is now SmsResponseEntity, no need to jsonDecode
          debugPrint('PhoneAuthenticationCubit: SMS sending succeeded: ${success.activated}');
          debugPrint('PhoneAuthenticationCubit: SMS sent successfully to $phoneNumber');
          emit(AuthSmsSent(phoneNumber: phoneNumber));
        },
      );
    } catch (e) {
      debugPrint('PhoneAuthenticationCubit: Unexpected error: $e');
      emit(AuthSmsError('Une erreur inattendue s\'est produite: $e', phoneNumber: phoneNumber));
    }
  }

  /// Set the activation code in the state
  ///
  /// [activationCode] - The entered activation code
  void setActivationCode(String activationCode) {
    if (state is AuthSmsSent) {
      final currentState = state as AuthSmsSent;
      emit(AuthSmsSent(phoneNumber: currentState.phoneNumber, activationCode: activationCode));
    }
  }
  
  /// Reset to initial state
  void reset() {
    emit(const AuthInitial());
  }

  /// Resend SMS verification code to the provided phone number
  ///
  /// [phoneNumber] - The complete phone number including country code
  Future<void> resendSms(String phoneNumber) async {
    try {


      // Indicate resending is in progress
      emit(const ResendingAuthSms());

      debugPrint('PhoneAuthenticationCubit: Resending SMS to $phoneNumber');

      // Call the use case to resend SMS
      final result = await resendSmsUseCase(phoneNumber);

      result.fold(
        (failure) {
          debugPrint('PhoneAuthenticationCubit: SMS resending failed: ${failure.message}');
          // Emit error state on failure
          emit(ResendAuthSmsError(failure.message));
        },
        (success) {
          // SMS resent successfully
          debugPrint('PhoneAuthenticationCubit: SMS resent successfully to $phoneNumber');
          // Emit success state on success
          emit(const ResendAuthSmsSuccess());
        },
      );
    } catch (e) {
      debugPrint('PhoneAuthenticationCubit: Unexpected error during resend: $e');
      // Emit error state for unexpected errors
      emit(ResendAuthSmsError('Une erreur inattendue s\'est produite lors du renvoi du SMS: $e'));
    }
  }

  /// Handle the response from the reactivate dialog
  ///
  /// [phoneNumber] - The complete phone number including country code
  /// [activated] - Whether the user chose to reactivate (true) or not (false)
  Future<void> checkResponse(String phoneNumber, {required bool activated}) async {
    try {
      emit(const AuthSmsSending());

      debugPrint('PhoneAuthenticationCubit: Checking response for $phoneNumber with activated: $activated');

      // Call the use case to check the response
      final result = await checkResponseUseCase(phoneNumber, activated: activated);

      result.fold(
        (failure) {
          debugPrint('PhoneAuthenticationCubit: Check response failed: ${failure.message}');
          emit(AuthSmsError(failure.message, phoneNumber: phoneNumber));
        },
        (success) {
          // Response checked successfully
          debugPrint('PhoneAuthenticationCubit: Check response succeeded: $success'); // Removed jsonDecode
          debugPrint('PhoneAuthenticationCubit: Response checked successfully for $phoneNumber');
          emit(AuthSmsSent(phoneNumber: phoneNumber)); // Assuming AuthSmsSent is the correct state after this
        },
      );
    } catch (e) {
      debugPrint('PhoneAuthenticationCubit: Unexpected error during check response: $e');
      emit(AuthSmsError('Une erreur inattendue s\'est produite lors de la vérification de la réponse: $e', phoneNumber: phoneNumber));
    }
  }
}
