

import 'package:Kairos/core/device_info.dart';

class CheckAppRequest extends DeviceInfo {
    final bool activated;    CheckAppRequest({
        required this.activated,
        required super.numeroTelephone,
        required super.marqueTelephone,
        required super.modelTelephone,
        required super.imeiTelephone,
        required super.numeroSerie,
    });

    factory CheckAppRequest.fromJson(Map<String, dynamic> json) {
        return CheckAppRequest(
            activated: json['activated'],
            numeroTelephone: json['numeroTelephone'],
            marqueTelephone: json['marqueTelephone'],
            modelTelephone: json['modelTelephone'],
            imeiTelephone: json['imeiTelephone'],
            numeroSerie: json['numeroSerie'],
        );
    }

    @override
    Map<String, dynamic> toJson() {
        final json = super.toJson();
        json.addAll({
            'activated': activated,
        });
        return json;
    }
}
