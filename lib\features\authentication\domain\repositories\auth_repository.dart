import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/user.dart';
import '../entities/sms_response_entity.dart'; // Import the new entity

/// Abstract repository interface for authentication operations
abstract class AuthRepository {
  /// Login with email and password
  Future<Either<Failure, User?>> login(String email, String password);
  
  /// Logout current user
  Future<Either<Failure, void>> logout();
  
  /// Check if user is authenticated
  Future<Either<Failure, bool>> isAuthenticated();
  
  /// Get current user
  Future<Either<Failure, User?>> getCurrentUser();
  
  /// Send SMS for verification
  Future<Either<Failure, SmsResponseEntity>> sendSms(String phoneNumber); // Update return type
  
  /// Refresh authentication token
  Future<Either<Failure, String>> refreshToken();
  
  /// Activate user account
  Future<Either<Failure, void>> activateAccount(String activationCode);

  /// Resend SMS for verification
  Future<Either<Failure, dynamic>> resendSms(String phoneNumber);

  /// Verify PIN code with full name and device info
  Future<Either<Failure, dynamic>> verifyPinWithDetails(String fullName, String otp, String phoneNumber);

  /// Check the user's response to the reactivation dialog
  Future<Either<Failure, dynamic>> checkResponse(String phoneNumber, {required bool activated});
}
