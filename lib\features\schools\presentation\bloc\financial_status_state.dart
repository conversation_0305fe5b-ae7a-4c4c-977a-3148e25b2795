import 'package:equatable/equatable.dart';
import '../../domain/entities/financial_status_entity.dart';

/// Base financial status state
abstract class FinancialStatusState extends Equatable {
  const FinancialStatusState();
  
  @override
  List<Object?> get props => [];
}

/// Initial financial status state
class FinancialStatusInitial extends FinancialStatusState {
  const FinancialStatusInitial();
}

/// Loading state during financial status check
class FinancialStatusLoading extends FinancialStatusState {
  final String codeEtudiant;

  const FinancialStatusLoading({required this.codeEtudiant});

  @override
  List<Object?> get props => [codeEtudiant];
}

/// Financial status check success
class FinancialStatusSuccess extends FinancialStatusState {
  final FinancialStatusEntity financialStatus;
  
  const FinancialStatusSuccess({required this.financialStatus});
  
  @override
  List<Object?> get props => [financialStatus];
}

/// Financial status check error
class FinancialStatusError extends FinancialStatusState {
  final String message;

  const FinancialStatusError(this.message);

  @override
  List<Object?> get props => [message];
}
