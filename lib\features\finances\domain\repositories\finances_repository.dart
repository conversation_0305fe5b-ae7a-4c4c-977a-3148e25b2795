import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/frais_etudiant_entity.dart';
import '../entities/finances_response_entity.dart';
import '../entities/finances_unpaid_response_entity.dart';

/// Abstract repository interface for finances operations
abstract class FinancesRepository {
  /// Get list of paid fees for a student
  Future<Either<Failure, FinancesResponseEntity>> getPaidFees({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get filtered paid fees for a student with date range
  Future<Either<Failure, FinancesResponseEntity>> getFilteredPaidFees({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String dateDebut,
    required String dateFin,
    String? codeUtilisateur,
  });

  /// Get list of unpaid fees for a student
  Future<Either<Failure, FinancesUnpaidResponseEntity>> getUnpaidFees({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}
