import '../../domain/entities/notes_evaluation_entity.dart';

/// Data model for notes evaluation
class NotesEvaluationModel {
  final String typeDevoir;
  final String dateDevoir;
  final String classe;
  final String semestre;
  final String cours;
  final String professeur;
  final double note;
  final double moyenneClasse;
  final double noteMin;
  final double noteMax;

  const NotesEvaluationModel({
    required this.typeDevoir,
    required this.dateDevoir,
    required this.classe,
    required this.semestre,
    required this.cours,
    required this.professeur,
    required this.note,
    required this.moyenneClasse,
    required this.noteMin,
    required this.noteMax,
  });

  /// Create model from JSON
  factory NotesEvaluationModel.fromJson(Map<String, dynamic> json) {
    return NotesEvaluationModel(
      typeDevoir: json['typeDevoir'] ?? '',
      dateDevoir: json['dateDevoir'] ?? '',
      classe: json['classe'] ?? '',
      semestre: json['semestre'] ?? '',
      cours: json['cours'] ?? '',
      professeur: json['professeur'] ?? '',
      note: (json['note'] ?? 0.0).toDouble(),
      moyenneClasse: (json['moyenneClasse'] ?? 0.0).toDouble(),
      noteMin: (json['noteMin'] ?? 0.0).toDouble(),
      noteMax: (json['noteMax'] ?? 0.0).toDouble(),
    );
  }

  /// Convert model to entity
  NotesEvaluationEntity toEntity() {
    return NotesEvaluationEntity(
      typeDevoir: typeDevoir,
      dateDevoir: dateDevoir,
      classe: classe,
      semestre: semestre,
      cours: cours,
      professeur: professeur,
      note: note,
      moyenneClasse: moyenneClasse,
      noteMin: noteMin,
      noteMax: noteMax,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'typeDevoir': typeDevoir,
      'dateDevoir': dateDevoir,
      'classe': classe,
      'semestre': semestre,
      'cours': cours,
      'professeur': professeur,
      'note': note,
      'moyenneClasse': moyenneClasse,
      'noteMin': noteMin,
      'noteMax': noteMax,
    };
  }
}
