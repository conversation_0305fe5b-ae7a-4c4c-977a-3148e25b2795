/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;
  
  const AppException(this.message, [this.code]);
  
  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Server-related exceptions
class ServerException extends AppException {
  final int? returnCode; // Add returnCode property

  const ServerException(super.message, [super.code, this.returnCode]); // Add returnCode to constructor

  @override
  String toString() => 'ServerException: $message${code != null ? ' (Code: $code)' : ''}${returnCode != null ? ' (Return Code: $returnCode)' : ''}'; // Update toString
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message, [super.code]);
}

/// Cache-related exceptions
class CacheException extends AppException {
  const CacheException(super.message, [super.code]);
}

/// Validation-related exceptions
class ValidationException extends AppException {
  const ValidationException(super.message, [super.code]);
}

/// Authentication-related exceptions
class AuthenticationException extends AppException {
  const AuthenticationException(super.message, [super.code]);
}

/// Authorization-related exceptions
class AuthorizationException extends AppException {
  const AuthorizationException(super.message, [super.code]);
}

/// Format-related exceptions
class FormatException extends AppException {
  const FormatException(super.message, [super.code]);
}

/// Timeout-related exceptions
class TimeoutException extends AppException {
  const TimeoutException(super.message, [super.code]);
}
