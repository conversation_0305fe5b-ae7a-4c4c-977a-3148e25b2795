import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/profile_repository.dart';
import '../../../authentication/data/models/deconnexion_request.dart';

/// Use case for logging out user (calling suppressionEtablissement API)
class LogoutUseCase implements UseCase<void, DeconnexionRequest> {
  final ProfileRepository repository;

  LogoutUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(DeconnexionRequest params) async {
    // First call the API
    final apiResult = await repository.logout(params);
    
    return apiResult.fold(
      (failure) => Left(failure),
      (_) async {
        // If API call successful, remove profile from local storage
        final removeResult = await repository.removeProfile();
        return removeResult.fold(
          (failure) => Left(failure),
          (_) => const Right(null),
        );
      },
    );
  }
}
