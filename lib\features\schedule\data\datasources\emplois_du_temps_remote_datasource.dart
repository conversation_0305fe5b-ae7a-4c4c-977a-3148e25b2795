import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../models/emploi_temps_item.dart';

/// Abstract interface for schedule remote data source
abstract class EmploisDuTempsRemoteDataSource {
  /// Get schedule/timetable data from API
  Future<List<List<EmploiTempsItem>>> getEmploisDuTemps({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get filtered schedule/timetable data from API
  Future<List<List<EmploiTempsItem>>> getEmploisDuTempsFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  });
}

/// Implementation of EmploisDuTempsRemoteDataSource
class EmploisDuTempsRemoteDataSourceImpl implements EmploisDuTempsRemoteDataSource {
  final ApiClient apiClient;

  EmploisDuTempsRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<List<EmploiTempsItem>>> getEmploisDuTemps({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur for PAR profile if provided
      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      debugPrint('EmploisDuTempsRemoteDataSource: Calling getEmploisDuTemps with params: $queryParameters');

      // Make HTTP GET request to the emploisDuTemps endpoint
      final response = await apiClient.getWithToken(
        ApiEndpoints.emploisDuTemps,
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes)
      );

      debugPrint('EmploisDuTempsRemoteDataSource: Received response with status code: ${response.statusCode}');

      final String responseJsonString = latin1.decode(response.data);
      debugPrint('EmploisDuTempsRemoteDataSource: Decoded response string: $responseJsonString');

      final decodedResponse = jsonDecode(responseJsonString);
      debugPrint('EmploisDuTempsRemoteDataSource: Decoded JSON object: $decodedResponse');

      // Parse response as map of date -> list of items
      if (decodedResponse is Map<String, dynamic>) {
        final entries = decodedResponse.entries.toList()
          ..sort((a, b) => a.key.compareTo(b.key)); // sort by date ascending
        final List<List<EmploiTempsItem>> scheduleData = entries.map<List<EmploiTempsItem>>((entry) {
          final daySchedule = entry.value;
          if (daySchedule is List) {
            return daySchedule.map<EmploiTempsItem>((item) => EmploiTempsItem.fromJson(item)).toList();
          }

          return <EmploiTempsItem>[];
        }).toList();
        debugPrint('EmploisDuTempsRemoteDataSource: Parsed schedule data: $scheduleData');
        return scheduleData;
      }

      return [];
    } catch (e) {
      debugPrint('EmploisDuTempsRemoteDataSource: Error getting schedule data: $e');
      throw Exception('Failed to get schedule data: $e');
    }
  }

  @override
  Future<List<List<EmploiTempsItem>>> getEmploisDuTempsFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur for PAR profile if provided
      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      // Add date filters if provided
      if (startDate != null && startDate.isNotEmpty) {
        queryParameters['dateDebut'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        queryParameters['dateFin'] = endDate;
      }

      debugPrint('EmploisDuTempsRemoteDataSource: Calling getEmploisDuTempsFiltres with params: $queryParameters');

      // Make HTTP GET request to the emploisDuTempsFiltres endpoint
      final response = await apiClient.getWithToken(
        ApiEndpoints.emploisDuTempsFiltres,
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes)
      );

      debugPrint('EmploisDuTempsRemoteDataSource: Received filtered response with status code: ${response.statusCode}');

      final String responseJsonString = latin1.decode(response.data);
      final decodedResponse = jsonDecode(responseJsonString);

      // Parse response as map of date -> list of items
      if (decodedResponse is Map<String, dynamic>) {
        final entries = decodedResponse.entries.toList()
          ..sort((a, b) => a.key.compareTo(b.key)); // sort by date ascending
        return entries.map<List<EmploiTempsItem>>((entry) {
          final daySchedule = entry.value;
          if (daySchedule is List) {
            return daySchedule.map<EmploiTempsItem>((item) => EmploiTempsItem.fromJson(item)).toList();
          }
          return <EmploiTempsItem>[];
        }).toList();
      }

      return [];
    } catch (e) {
      debugPrint('EmploisDuTempsRemoteDataSource: Error getting filtered schedule data: $e');
      throw Exception('Failed to get filtered schedule data: $e');
    }
  }
}
