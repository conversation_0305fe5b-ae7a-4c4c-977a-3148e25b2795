import 'package:Kairos/features/schools/data/datasources/schools_local_datasource.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/etablissement_entity.dart';
import '../../domain/entities/financial_status_entity.dart';
import '../../domain/repositories/schools_repository.dart';
import '../datasources/schools_remote_datasource.dart';
import '../models/user_profile_model.dart';

/// Implementation of SchoolsRepository
class SchoolsRepositoryImpl implements SchoolsRepository {
  final SchoolsRemoteDataSource remoteDataSource;
  final SchoolsLocalDataSource localDataSource;

  SchoolsRepositoryImpl({required this.remoteDataSource, required this.localDataSource});

  @override
  Future<Either<Failure, List<EtablissementEntity>>> getAllAvailableSchools() async {
    try {
      final schools = await remoteDataSource.getAllAvailableSchools();
      // Map Etablissement models to EtablissementEntity entities
      final List<EtablissementEntity> schoolEntities = schools.map((school) => school.toEntity()).toList();
      return Right(schoolEntities);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
       // Handle Dio errors (HTTP errors, network issues, etc.)
      return Left(ServerFailure('Failed to load schools: ${e.message}'));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<EtablissementUtilisateur>>> getUserSchools(String phoneNumber) async {
    try {
      final userSchools = await remoteDataSource.getUserSchools(phoneNumber);
      // Iterate through the list of user profiles and store each one in Shared Preferences
      for (var userProfile in userSchools) {
        try {
          // Use the codeUtilisateur as the key for storing in Shared Preferences
          await localDataSource.storeUserProfile(userProfile);
          debugPrint("Successfully stored user profile with codeUtilisateur: ${userProfile.codeUtilisateur}");
        } catch (e) {
          // Log any errors during the storage process
          debugPrint("Error storing user profile with codeUtilisateur ${userProfile.codeUtilisateur}: $e");
        }
      }
      // Map UserProfileModel to EtablissementUtilisateur entities
      final List<EtablissementUtilisateur> userSchoolEntities = userSchools.map((school) => school.toEntity()).toList();
      return Right(userSchoolEntities);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
       // Handle Dio errors (HTTP errors, network issues, etc.)
      return Left(ServerFailure('Failed to load user schools: ${e.message}'));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  /// Get raw user profile models for authentication validation
  /// Optionally filter by codeUtilisateur
  @override
  Future<Either<Failure, List<UserProfileModel>>> getUserProfileModels(String phoneNumber, {String? codeUtilisateur}) async {
    try {
      // Fetch all user profiles for the given phone number
      final userProfiles = await remoteDataSource.getUserSchools(phoneNumber);

      // If codeUtilisateur is provided, filter the list
      if (codeUtilisateur != null) {
        final filteredProfiles = userProfiles.where(
          (profile) => profile.codeUtilisateur == codeUtilisateur,
        ).toList();
        // Return the filtered list (will be empty if no match is found)
        return Right(filteredProfiles);
      } else {
        // If codeUtilisateur is not provided, return the full list
        return Right(userProfiles);
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
       // Handle Dio errors (HTTP errors, network issues, etc.)
      return Left(ServerFailure('Failed to load user profiles: ${e.message}'));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, FinancialStatusEntity>> checkFinancialStatus({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      final financialStatus = await remoteDataSource.checkFinancialStatus(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );
      debugPrint('REPO --> FinancialStatusModel: $financialStatus');
      // Convert model to entity
      final FinancialStatusEntity financialStatusEntity = financialStatus.toEntity();
      return Right(financialStatusEntity);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
       // Handle Dio errors (HTTP errors, network issues, etc.)
      return Left(ServerFailure('Failed to check financial status: ${e.message}'));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }
}