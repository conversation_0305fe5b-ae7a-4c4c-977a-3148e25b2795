import 'package:equatable/equatable.dart';

/// Base authentication state
abstract class AuthState extends Equatable {
  const AuthState();
  
  @override
  List<Object?> get props => [];
}

/// Initial authentication state
class AuthInitial extends AuthState {
  const AuthInitial();
}

/// Loading state during authentication operations
class AuthLoading extends AuthState {
  const AuthLoading();
}

/// User is authenticated
class AuthAuthenticated extends AuthState {
  final String userId;
  
  const AuthAuthenticated({required this.userId});
  
  @override
  List<Object?> get props => [userId];
}

/// User is not authenticated
class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

/// User registration completed
class AuthRegistered extends AuthState {
  const AuthRegistered();
}

/// PIN verification completed
class AuthPinVerified extends AuthState {
  const AuthPinVerified();
}

/// Authentication error occurred
class AuthError extends AuthState {
  final String message;

  const AuthError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Phone authentication specific states
/// SMS sending in progress
class AuthSmsSending extends AuthState {
  const AuthSmsSending();
}

/// SMS sent successfully
class AuthSmsSent extends AuthState {
  final String phoneNumber;
  final String? activationCode; // Add activation code field

  const AuthSmsSent({required this.phoneNumber, this.activationCode});

  @override
  List<Object?> get props => [phoneNumber, activationCode];
}

/// SMS sending failed
class AuthSmsError extends AuthState {
  final String message;
  final String? phoneNumber;

  const AuthSmsError(this.message, {this.phoneNumber});

  @override
  List<Object?> get props => [message, phoneNumber];
}


/// SMS resending in progress
class ResendingAuthSms extends AuthState {
  const ResendingAuthSms();
}

/// SMS resend successful
class ResendAuthSmsSuccess extends AuthState {
  const ResendAuthSmsSuccess();
}

/// SMS resend failed
class ResendAuthSmsError extends AuthState {
  final String message;

  const ResendAuthSmsError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Code activation specific states
/// Code activation in progress
class CodeActivationLoadingState extends AuthState {
  const CodeActivationLoadingState();
}

/// Code activation successful
class CodeActivationSuccessState extends AuthState {
  // Optionally include data from the success response if needed
  
  const CodeActivationSuccessState();
}

/// Code activation failed
class CodeActivationErrorState extends AuthState {
  final int? returnCode;
  final String userMessage;

  const CodeActivationErrorState({this.returnCode, required this.userMessage});

  @override
  List<Object?> get props => [returnCode, userMessage];
}
