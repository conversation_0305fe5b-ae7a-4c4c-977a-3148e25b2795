import 'package:Kairos/core/device_info.dart';
import 'package:Kairos/features/schools/data/models/activation_request.dart';

class ActivationRequestEntity {
  final String codeEtab;
  final String username;
  final String password;

  const ActivationRequestEntity({
    required this.codeEtab,
    required this.username,
    required this.password,
  });

  Map<String, dynamic> to<PERSON><PERSON>() => {
        'codeEtab': codeEtab,
        'username': username,
        'password': password,
      };
  /// Converts the entity to an ActivationRequest model.
  /// Retrieves device information from the provided DeviceInfoService.
  ActivationRequest toModel(DeviceInfo deviceInfo, String numeroTelephone) {

    return ActivationRequest(
      codeEtab: codeEtab,
      password: password,
      codeUtilisateur: username, // Map username to codeUtilisateur
      numeroTelephone: numeroTelephone,
      marqueTelephone: deviceInfo.marqueTelephone,
      modelTelephone: deviceInfo.modelTelephone,
      imeiTelephone: deviceInfo.imeiTelephone,
      numeroSerie: deviceInfo.numeroSerie,
    );

  }
}
