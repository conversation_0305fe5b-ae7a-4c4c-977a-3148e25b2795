import '../../domain/entities/frais_etudiant_entity.dart';

/// Data model for student fee information from API
class FraisEtudiantModel {
  final String intituleFrais;
  final String? moisConcerne;
  final num? montantTotalFrais;
  final num? montantFrais;
  final String? dateEchance;
  final String? dateEchanceFrais;
  final String? numeroQuittance;
  final String? dateQuittance;
  final bool? indicateur;
  final num? montantEncaisseAjour;
  final String? dateDernierPaiement;

  const FraisEtudiantModel({
    required this.intituleFrais,
    this.moisConcerne,
    this.montantTotalFrais,
    this.montantFrais,
    this.dateEchance,
    this.dateEchanceFrais,
    this.numeroQuittance,
    this.dateQuittance,
    this.indicateur,
    this.montantEncaisseAjour,
    this.dateDernierPaiement,
  });

  /// Create model from JSON
  factory FraisEtudiantModel.fromJson(Map<String, dynamic> json) {
    return FraisEtudiantModel(
      intituleFrais: json['intituleFrais'] as String,
      moisConcerne: json['moisConcerne'] as String?,
      montantTotalFrais: json['montantTotalFrais'] as num?,
      montantFrais: json['montantFrais'] as num?,
      dateEchance: json['dateEchance'] as String?,
      dateEchanceFrais: json['dateEchanceFrais'] as String?,
      numeroQuittance: json['numeroQuittance'] as String?,
      dateQuittance: json['dateQuittance'] as String?,
      indicateur: json['indicateur'] as bool?,
      montantEncaisseAjour: json['montantEncaisseAjour'] as num?,
      dateDernierPaiement: json['dateDernierPaiement'] as String?,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'intituleFrais': intituleFrais,
      'moisConcerne': moisConcerne,
      'montantTotalFrais': montantTotalFrais,
      'montantFrais': montantFrais,
      'dateEchance': dateEchance,
      'dateEchanceFrais': dateEchanceFrais,
      'numeroQuittance': numeroQuittance,
      'dateQuittance': dateQuittance,
      'indicateur': indicateur,
      'montantEncaisseAjour': montantEncaisseAjour,
      'dateDernierPaiement': dateDernierPaiement,
    };
  }

  /// Convert model to entity
  FraisEtudiantEntity toEntity() {
    return FraisEtudiantEntity(
      intituleFrais: intituleFrais,
      moisConcerne: moisConcerne,
      montantTotalFrais: montantTotalFrais,
      montantFrais: montantFrais,
      dateEchance: dateEchance,
      dateEchanceFrais: dateEchanceFrais,
      numeroQuittance: numeroQuittance,
      dateQuittance: dateQuittance,
      indicateur: indicateur,
      montantEncaisseAjour: montantEncaisseAjour,
      dateDernierPaiement: dateDernierPaiement,
    );
  }
}
