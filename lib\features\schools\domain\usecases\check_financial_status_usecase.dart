import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/financial_status_entity.dart';
import '../repositories/schools_repository.dart';

/// Parameters for checking financial status
class CheckFinancialStatusParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  const CheckFinancialStatusParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });
}

/// Use case for checking student financial status
class CheckFinancialStatusUseCase {
  final SchoolsRepository repository;

  const CheckFinancialStatusUseCase(this.repository);

  /// Execute the use case to check financial status
  Future<Either<Failure, FinancialStatusEntity>> call(
    CheckFinancialStatusParams params,
  ) async {
    return await repository.checkFinancialStatus(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}
