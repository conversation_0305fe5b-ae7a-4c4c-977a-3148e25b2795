import 'package:equatable/equatable.dart';

/// Model for the activate school request payload.
class ActivateSchoolRequest extends Equatable {
  final String codeEtab;
  final String username;
  final String password;

  const ActivateSchoolRequest({
    required this.codeEtab,
    required this.username,
    required this.password,
  });

  /// Converts the object to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'codeEtab': codeEtab,
      'username': username,
      'password': password,
    };
  }

  @override
  List<Object?> get props => [codeEtab, username, password];
}