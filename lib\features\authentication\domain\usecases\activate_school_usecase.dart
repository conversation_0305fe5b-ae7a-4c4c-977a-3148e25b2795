import 'package:Kairos/features/authentication/domain/entities/activation_request_entity.dart';
import 'package:Kairos/features/authentication/domain/repositories/activate_school_repository.dart';
import 'package:Kairos/features/schools/data/models/user_profile_model.dart';
import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';

/// Use case for activating a school.
class ActivateSchoolUseCase implements UseCase<void, ActivationRequestEntity> {
  final ActivateSchoolRepository repository;

  ActivateSchoolUseCase(this.repository);

  @override
  Future<Either<Failure, List<UserProfileModel>>> call(ActivationRequestEntity params) async {
    // Call the repository method to activate the school
    return await repository.activateSchool(params);
  }
}