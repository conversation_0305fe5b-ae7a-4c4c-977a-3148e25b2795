import 'package:equatable/equatable.dart';
import '../../domain/entities/absence_retard_entity.dart';

/// Base absences retards state
abstract class AbsencesRetardsState extends Equatable {
  const AbsencesRetardsState();
  
  @override
  List<Object?> get props => [];
}

/// Initial absences retards state
class AbsencesRetardsInitial extends AbsencesRetardsState {
  const AbsencesRetardsInitial();
}

/// Loading state during absences retards operations
class AbsencesRetardsLoading extends AbsencesRetardsState {
  const AbsencesRetardsLoading();
}

/// Absences retards data loaded successfully
class AbsencesRetardsLoaded extends AbsencesRetardsState {
  final List<AbsenceRetardEntity> absencesRetards;
  
  const AbsencesRetardsLoaded({required this.absencesRetards});
  
  @override
  List<Object?> get props => [absencesRetards];
}

/// Absences retards error occurred
class AbsencesRetardsError extends AbsencesRetardsState {
  final String message;
  
  const AbsencesRetardsError(this.message);
  
  @override
  List<Object?> get props => [message];
}
