﻿import 'package:equatable/equatable.dart';

/// Base course_log state
abstract class CourseLogState extends Equatable {
  const CourseLogState();
  
  @override
  List<Object?> get props => [];
}

/// Initial course_log state
class CourseLogInitial extends CourseLogState {
  const CourseLogInitial();
}

/// Loading state during course_log operations
class CourseLogLoading extends CourseLogState {
  const CourseLogLoading();
}

/// CourseLog data loaded successfully
class CourseLogLoaded extends CourseLogState {
  final List<dynamic> data; // TODO: Replace with proper type
  
  const CourseLogLoaded({required this.data});
  
  @override
  List<Object?> get props => [data];
}

/// CourseLog error occurred
class CourseLogError extends CourseLogState {
  final String message;
  
  const CourseLogError(this.message);
  
  @override
  List<Object?> get props => [message];
}
