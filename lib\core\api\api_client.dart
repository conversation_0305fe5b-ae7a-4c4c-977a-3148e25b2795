import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:dio/dio.dart';

/// Central API client for handling HTTP requests
/// This class provides a configured Dio instance with interceptors
class ApiClient {
  static const String baseUrl = 'https://kairos-mobile.sensoft-labs.com/api';
  late final Dio _dio;
  final AuthLocalDataSource authLocalDataSource;

  

  ApiClient({required this.authLocalDataSource}) {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 120),
      // Increased receive timeout to 5 minutes to potentially mitigate FormatException due to incomplete response
      receiveTimeout: const Duration(seconds: 300),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    _setupInterceptors();
   
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (object) {
          // TODO: Use proper logging instead of print
          print(object);
        },
      ),
    );

    // TODO: Add authentication interceptor
    // TODO: Add error handling interceptor
  }

  Dio get dio => _dio;

  // Common HTTP methods
  Future<Response> get(String path, {Map<String, dynamic>? queryParameters}) {
    return _dio.get(path, queryParameters: queryParameters);
  }

  Future<Response> post(String path, {dynamic data}) {
    return _dio.post(path, data: data);
  }

  Future<Response> put(String path, {dynamic data}) {
    return _dio.put(path, data: data);
  }

  Future<Response> delete(String path) {
    return _dio.delete(path);
  }

  /// POST request with JWT token if required
  Future<Response> postWithToken(String path, {dynamic data}) async {
    final token = await authLocalDataSource.getCachedToken();
    final headers = <String, dynamic>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null && token.isNotEmpty) 'token': token,
    };
    return _dio.post(
      path,
      data: data,
      options: Options(headers: headers),
    );
  }

  /// GET request with JWT token if required
  Future<Response> getWithToken(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    final token = await authLocalDataSource.getCachedToken();
    final headers = <String, dynamic>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null && token.isNotEmpty) 'token': token,
    };

    // Merge provided options with default headers and responseType
    final mergedOptions = (options ?? Options()).copyWith(
      headers: {
        ...?options?.headers,
        ...headers,
      },
      responseType: options?.responseType ?? ResponseType.json,
    );

    return _dio.get(
      path,
      queryParameters: queryParameters,
      options: mergedOptions,
    );
  }
}
