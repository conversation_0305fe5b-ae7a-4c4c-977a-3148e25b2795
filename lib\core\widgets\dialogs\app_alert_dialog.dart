import 'package:flutter/material.dart';
import 'package:Kairos/core/theme/color_schemes.dart';

/// A reusable custom AlertDialog component.
///
/// This dialog displays a title, content, a cancel button, and a primary action button.
/// The primary action button is styled with AppColorSchemes.errorRed and executes
/// the provided [primaryActionCallback].
class AppAlertDialog extends StatelessWidget {
  /// The title of the dialog.
  final String title;

  /// The content widget to display in the dialog body.
  final Widget content;

  /// The callback function to execute when the primary action button is pressed.
  final VoidCallback primaryActionCallback;

  /// The text to display on the primary action button. Defaults to 'CONFIRMER'.
  final String primaryActionText;

  /// The text to display on the cancel button. Defaults to 'ANNULER'.
  final String cancelActionText;

  /// Creates an [AppAlertDialog].
  const AppAlertDialog({
    super.key,
    required this.title,
    required this.content,
    required this.primaryActionCallback,
    this.primaryActionText = 'CONFIRMER',
    this.cancelActionText = 'ANNULER',
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        AlertDialog(
          backgroundColor: Colors.white,
          icon: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [

              Material(
                    color: Theme.of(context).colorScheme.secondary, // Use secondary color as background
                    shape: const CircleBorder(),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(20),
                      onTap: () {
                        Navigator.of(context).pop(); // Dismiss the dialog
                      },
                      child: const Padding(
                        padding: EdgeInsets.all(4.0),
                        child: Icon(Icons.close, size: 24, color: Colors.white), // White icon
                      ),
                    ),
                  ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: Align(
                      alignment: Alignment.center,
                      child: Image.asset(
                        "assets/icons/information-alert.png",
                        width: 35,
                        height: 35,
                      ),
                    ),
                  ),
                  
                ],
              ),
            ],
          ),
          title: Text(
            title,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          insetPadding: const EdgeInsets.symmetric(horizontal: 10),
          content: content,
          actionsAlignment: MainAxisAlignment.spaceBetween, // Align buttons to start and end
          actionsPadding: const EdgeInsets.all(15.0),
          actions: [
            // Primary action button (Left-aligned)
            FilledButton(
              style: ButtonStyle(
                foregroundColor: WidgetStateProperty.all(Colors.white),
                minimumSize: WidgetStateProperty.all(const Size(150, 50)),
                backgroundColor: WidgetStateProperty.all(AppColorSchemes.errorRed),
              ),
              onPressed: () {
                Navigator.of(context).pop(); // Dismiss the dialog first
                primaryActionCallback(); // Execute the provided callback
              },
              child: Text(primaryActionText),
            ),
            // Cancel button (Right-aligned)
            FilledButton(
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                minimumSize: WidgetStateProperty.all(const Size(150, 50)), // Ensure consistent size
              ),
              onPressed: () {
                Navigator.of(context).pop(); // Dismiss the dialog
              },
              child: Text(cancelActionText),
            ),
          ],
        ),
      ],
    );
  }
}