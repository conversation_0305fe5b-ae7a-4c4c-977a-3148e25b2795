

import 'package:Kairos/core/device_info.dart';

class DemandeActivationRequest extends DeviceInfo {
  final String codeEtab;
  final String codeUtilisateur;

  DemandeActivationRequest({
    required this.codeEtab,
    required this.codeUtilisateur,
    required super.numeroTelephone,
    required super.marqueTelephone,
    required super.modelTelephone,
    required super.imeiTelephone,
    required super.numeroSerie,
  });

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'codeEtab': codeEtab,
      'codeUtilisateur': codeUtilisateur,
    });
    return json;
  }
}
