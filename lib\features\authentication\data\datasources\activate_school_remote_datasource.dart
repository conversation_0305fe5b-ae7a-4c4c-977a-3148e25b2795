




import 'package:Kairos/core/api/api_client.dart';
import 'package:Kairos/core/api/api_endpoints.dart';
import 'package:Kairos/core/api/api_exception.dart';
import 'package:Kairos/core/error/exceptions.dart';
import 'package:Kairos/features/schools/data/models/activation_request.dart';
import 'package:Kairos/features/schools/data/models/user_profile_model.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

abstract class ActivateSchoolRemoteDatasource {

  /// Activate a school with the provided credentials.
  Future<List<UserProfileModel>> activateSchool(ActivationRequest request);
}


class ActivateSchoolRemoteDatasourceImpl implements ActivateSchoolRemoteDatasource {
  final ApiClient apiClient;

  ActivateSchoolRemoteDatasourceImpl({required this.apiClient});

  @override
  Future<List<UserProfileModel>> activateSchool(ActivationRequest request) async {
    try {
      // Make HTTP POST request to the activateSchool endpoint with the request payload
      final response = await apiClient.postWithToken(
        ApiEndpoints.activateSchool,
        data: request.toJson(),
      );
      debugPrint('ActivateSchoolRemoteDatasourceImpl: activate school response: ${response.statusCode}, ${response.data}');

      // TODO: Handle successful activation response if needed (e.g., parse response data)
      // For now, assuming a successful response (status code 200) is enough
      if (response.statusCode != 200 || (response.statusCode == 200 && response.data is Map<String, dynamic>)) {
         if (response.data is Map<String, dynamic> && 
         ((response.data.containsKey('returnCode') && response.data.containsKey('userMessage'))
         ||
         (response.data.containsKey('isAuthenticated') && response.data['isAuthenticated'] == false))
         ) {
           throw response.data.containsKey(' returnCode')? 
           ServerException(response.data['userMessage'], response.data['returnCode']):
           throw ServerException(response.data['message']);
        }
         throw ServerException('Failed to activate school with status code: ${response.statusCode}');
      }

      // Assuming the response data is a list of user profile objects
      if (response.data is List) {
        final List<dynamic> userProfileJsonList = response.data;

        // Parse the list of user profile objects
        final List<UserProfileModel> userProfiles = userProfileJsonList
            .map((json) => UserProfileModel.fromJson(json))
            .toList();

        return userProfiles;
      } else {
        // Handle unexpected response format
        throw ServerException('Erreur de format de réponse inattendue lors de l\'activation de l\'école. Veuillez réessayer plus tard.');
      }

    } on DioException catch (e) {
      debugPrint('ActivateSchoolRemoteDatasourceImpl: DioException: $e');
      debugPrint('ActivateSchoolRemoteDatasourceImpl: DioException response: ${e.response}');
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        debugPrint('ActivateSchoolRemoteDatasourceImpl: NetworkException: ${e.message}');
        throw ServerException('Erreur de connexion lors de l\'activation de l\'école: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de l\'activation de l\'école: $e');
    }
  }
  
  
  }