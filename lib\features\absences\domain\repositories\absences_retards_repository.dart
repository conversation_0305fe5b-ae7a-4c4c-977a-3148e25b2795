import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/absence_retard_entity.dart';

/// Abstract repository interface for absences and tardiness operations
abstract class AbsencesRetardsRepository {
  /// Get absences and tardiness records for a student
  /// 
  /// Parameters:
  /// - [codeEtab]: School code
  /// - [telephone]: Phone number
  /// - [codeEtudiant]: Student code
  /// - [codeUtilisateur]: User code (optional, used for PAR profile)
  Future<Either<Failure, List<AbsenceRetardEntity>>> getAbsencesRetards({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get filtered absences and tardiness records for a student
  /// 
  /// Parameters:
  /// - [codeEtab]: School code
  /// - [telephone]: Phone number
  /// - [codeEtudiant]: Student code
  /// - [codeUtilisateur]: User code (optional, used for PAR profile)
  /// - [startDate]: Start date for filtering (optional)
  /// - [endDate]: End date for filtering (optional)
  Future<Either<Failure, List<AbsenceRetardEntity>>> getAbsencesRetardsFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  });
}
