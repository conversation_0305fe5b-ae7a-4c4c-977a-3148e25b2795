import 'package:equatable/equatable.dart';

/// Entity representing a notes evaluation
class NotesEvaluationEntity extends Equatable {
  final String typeDevoir;
  final String dateDevoir;
  final String classe;
  final String semestre;
  final String cours;
  final String professeur;
  final double note;
  final double moyenneClasse;
  final double noteMin;
  final double noteMax;

  const NotesEvaluationEntity({
    required this.typeDevoir,
    required this.dateDevoir,
    required this.classe,
    required this.semestre,
    required this.cours,
    required this.professeur,
    required this.note,
    required this.moyenneClasse,
    required this.noteMin,
    required this.noteMax,
  });

  @override
  List<Object?> get props => [
        typeDevoir,
        dateDevoir,
        classe,
        semestre,
        cours,
        professeur,
        note,
        moyenneClasse,
        noteMin,
        noteMax,
      ];
}
