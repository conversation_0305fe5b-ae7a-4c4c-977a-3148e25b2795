{"v": "5.6.6", "ip": 0, "op": 149, "fr": 60, "w": 145, "h": 29, "layers": [{"ind": 411, "nm": "surface4389", "ao": 0, "ip": 0, "op": 720, "st": 0, "ty": 4, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.17], "y": [0]}, "t": 19, "s": [100]}, {"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.17], "y": [0]}, "t": 86, "s": [100]}, {"t": 108, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.67, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 0, "s": [90.68, 129], "to": [0, -46.53, 0], "ti": [0, 21.47, 0]}, {"t": 33, "s": [90.68, 18.06]}], "ix": 2}, "a": {"a": 0, "k": [72.5, 14.5], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.67, 0.67, 0.67], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 0, "s": [106, 106, 100]}, {"i": {"x": [0.67, 0.67, 0.67], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 33, "s": [93, 92, 100]}, {"t": 59, "s": [106, 106, 100]}], "ix": 6}}, "shapes": [{"ty": "gr", "hd": false, "nm": "surface4389", "it": [{"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.02, 0]], "o": [[0.02, 0.02], [0, 0]], "v": [[49.57, 10.79], [49.57, 10.79]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [1, 1, 1, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.09, -0.02], [0, -1.52], [0, 0], [-0.09, 0]], "o": [[-0.09, 0], [-0.02, 1.57], [0, 0], [0.09, -0.02], [0, 0]], "v": [[49.74, 6.12], [49.48, 6.15], [49.46, 10.76], [49.46, 6.15], [49.74, 6.12]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [1, 1, 1, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.82, -2.25], [0, 0], [-0.84, 2.32], [0, -0.02]], "o": [[-0.84, 2.3], [0, 0], [0.82, -2.25], [0.02, 0], [0, 0]], "v": [[26.53, 7.48], [24.07, 14.25], [24.05, 14.25], [26.51, 7.46], [26.53, 7.48]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [1, 1, 1, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "gr", "hd": false, "it": [{"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[41.94, 21.84], [38, 21.84], [38, 2.79], [41.94, 2.79]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [0.58, 0.63, 0.67, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "gr", "hd": false, "it": [{"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.1, 0.16], [0.82, 1.27], [0, 0], [1.41, 0.95], [0.05, 0.04], [-0.15, 0.05], [-0.35, 0.14], [-0.49, 1.33], [0, 0], [-0.06, 0.47], [0.41, 0.91], [1.46, 0.32], [1.21, 0], [2.62, 0], [0.18, -0.02], [0, 0], [0, -0.09], [0.02, -6.11], [-0.3, 0], [-1.01, -0.02], [0, 0], [-0.09, 0.02], [0, 0.24], [0, 0], [0, 0], [-0.52, -0.02], [0, 0], [-0.52, -0.54], [-0.38, -0.52], [-1.19, -1.75], [-0.19, -0.02], [-1.34, 0], [-0.11, 0.04]], "o": [[-0.14, -0.23], [-0.8, -1.29], [0, 0], [-0.81, -1.49], [-0.05, -0.04], [0.14, -0.09], [0.37, -0.12], [1.34, -0.46], [0, 0], [0.16, -0.45], [0.13, -0.99], [-0.52, -1.4], [-1.19, -0.23], [-2.62, -0.05], [-0.16, 0], [0, 0], [0, 0.12], [0, 6.11], [0, 0.35], [1.01, -0.02], [0, 0], [0.1, 0], [0.16, -0.07], [0, 0], [0, 0], [0.57, 0], [0, 0], [0.74, -0.01], [0.43, 0.48], [1.19, 1.75], [0.11, 0.15], [1.34, 0.04], [0.11, -0.01], [0, 0]], "v": [[62.61, 21.81], [62.26, 21.23], [59.85, 17.38], [59.85, 17.38], [56.48, 13.66], [56.32, 13.57], [56.76, 13.36], [57.88, 13.05], [60.76, 10.22], [60.76, 10.2], [61.08, 8.82], [60.65, 5.91], [57.47, 3.15], [53.86, 2.8], [46, 2.79], [45.52, 2.84], [45.5, 2.84], [45.48, 3.14], [45.48, 21.46], [45.92, 21.88], [48.98, 21.88], [48.97, 21.88], [49.25, 21.85], [49.44, 21.39], [49.44, 14.01], [49.46, 14.01], [51.06, 14.01], [51.07, 14.01], [53.04, 14.83], [54.24, 16.33], [57.79, 21.6], [58.25, 21.86], [62.27, 21.88], [62.6, 21.81]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [-0.02, 1.57], [0, 0], [-0.09, 0], [0, 0], [-1.64, -0.04], [0, 0], [0, 0], [-0.28, -0.84], [1.7, -0.12], [0.35, -0.02], [1.52, -0.04], [0, 0], [0.02, 0], [0.02, 0]], "o": [[0, -1.52], [0, 0], [0.09, -0.02], [0, 0], [1.64, 0], [0, 0], [0, 0], [0.88, -0.03], [0.4, 1.29], [-0.35, 0.02], [-1.52, 0.07], [0, 0], [-0.02, 0], [-0.02, -0.04], [0, 0]], "v": [[49.46, 10.76], [49.48, 6.15], [49.48, 6.15], [49.74, 6.12], [49.81, 6.12], [54.73, 6.15], [54.91, 6.15], [54.91, 6.15], [56.86, 7.51], [55.2, 10.64], [54.14, 10.71], [49.6, 10.81], [49.56, 10.81], [49.51, 10.8], [49.46, 10.76]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [0.58, 0.63, 0.67, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "gr", "hd": false, "it": [{"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [-0.91, 1], [-0.28, 0.36], [0, 0], [0.07, 0.44], [0, 0], [-0.31, 1.35], [-1.87, 1.46], [-1.41, 0.45], [-1.74, -0.25], [-1.84, -2.31], [-0.37, -0.68], [0.01, -0.07], [0, 0], [3.32, -0.05], [0, 0], [0.82, -1.43], [0, 0], [0.2, -1.29], [1.98, -1.6], [1.42, -0.44], [1.94, 0.34], [1.18, 0.68], [0.96, 1.93], [0.05, 0.17]], "o": [[0, 0], [0, 0], [1.35, 0.03], [0.23, -0.25], [0, 0], [-0.33, 0], [0, 0], [-0.21, -1.37], [0.52, -2.31], [1.14, -0.95], [1.68, -0.52], [2.93, 0.34], [0.45, 0.62], [0.01, 0.07], [0, 0], [-3.33, 0], [0, 0], [-1.64, 0.08], [0, 0], [0.23, 1.28], [-0.4, 2.52], [-1.1, 0.99], [-1.88, 0.6], [-1.34, -0.21], [-1.89, -1.04], [-0.09, -0.06], [0, 0]], "v": [[86.99, 15.42], [97.27, 15.42], [97.27, 15.42], [100.83, 13.9], [101.51, 13.03], [86.66, 13.03], [86.06, 12.58], [86.07, 12.57], [86.22, 8.46], [89.92, 2.62], [93.78, 0.52], [98.96, 0.11], [106.42, 4.25], [107.66, 6.2], [107.66, 6.43], [107.15, 6.43], [97.16, 6.45], [97.17, 6.45], [93.2, 8.86], [108.6, 8.86], [108.65, 12.73], [104.95, 19.12], [101.12, 21.3], [95.31, 21.68], [91.49, 20.34], [87.12, 15.76], [86.99, 15.42]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [0.58, 0.63, 0.67, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "gr", "hd": false, "it": [{"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.65, 1.61], [0.96, 2.41], [0.66, 1.64], [0.32, 0], [1.17, -0.02], [0, 0], [0.08, -0.12], [0.33, -0.86], [0.51, -1.29], [0.35, -0.89], [0.56, -1.45], [0.52, -1.33], [0, 0], [0.02, -0.04], [0.01, 0], [0.09, -0.19], [0, -0.02], [0.07, -0.18], [0, 0], [0, 0], [0.64, 0.99], [0.64, 1], [0.54, 0.89], [0, 0], [0, 0.04], [0, 0], [-0.06, 0.08], [-1.59, 1.66], [-0.61, 0.62], [0, 0], [-0.05, 0.12], [-0.01, 0], [0.14, 0], [1.41, -0.03], [0.18, -0.17], [1.26, -1.38], [1.22, -1.33], [0, 0], [-0.02, 2.48], [0, 0], [0.33, -0.02], [1, 0.04], [0.02, -0.31], [0, 0], [0, -0.04], [0, 0], [-0.01, -0.01], [-0.11, 0], [-1.25, 0], [0, 0], [-0.02, 1.61], [0, 0], [0, 0], [-0.14, 0.14], [-0.82, 0.84], [-0.23, -0.39], [0, 0], [-0.58, -1.03], [-0.29, 0.02], [-2.76, -0.02], [0, 0], [-0.05, 0.26], [-0.42, 1.22], [0, 0], [-0.21, -0.03], [-2.27, -0.02], [-0.11, -0.12], [0, 0], [-0.05, -0.18], [-0.39, -1.08], [0, 0], [-0.23, 0.04], [0, 0], [0, 0], [-0.13, 0.02], [0.23, 0.56]], "o": [[-0.64, -1.61], [-0.96, -2.39], [-0.64, -1.64], [-0.07, -0.19], [-1.17, 0.02], [0, 0], [-0.15, 0.01], [-0.37, 0.84], [-0.51, 1.31], [-0.35, 0.89], [-0.57, 1.45], [-0.52, 1.33], [0, 0], [-0.02, 0.05], [0, 0.01], [-0.07, 0.18], [0, 0.02], [-0.07, 0.16], [0, 0], [0, 0], [-0.64, -1], [-0.64, -0.99], [-0.57, -0.87], [0, 0], [-0.02, -0.04], [0, 0], [0, -0.1], [1.57, -1.66], [0.61, -0.63], [0, 0], [0.09, -0.1], [0, -0.01], [-0.25, 0], [-1.4, 0], [-0.25, 0.02], [-1.27, 1.36], [-1.19, 1.29], [0, 0], [0, -2.48], [0, 0], [-0.02, -0.28], [-1, 0.02], [-0.38, -0.02], [0, 0], [0, 0.04], [0, 0], [0, 0.02], [0.09, 0.11], [1.19, 0.02], [0, 0], [0, -1.61], [0, 0], [0, 0], [0.02, -0.2], [0.8, -0.86], [0.31, -0.33], [0, 0], [0.61, 1.03], [0.12, 0.26], [2.76, -0.02], [0, 0], [0.26, 0.04], [0.44, -1.22], [0, 0], [0.04, -0.21], [2.27, 0], [0.14, 0], [0, 0], [0.09, 0.16], [0.42, 1.07], [0, 0], [0.05, 0.23], [0, 0], [0, 0], [0.13, -0.01], [-0.25, -0.61], [0, 0]], "v": [[35.59, 20.1], [33.66, 15.3], [30.79, 8.11], [28.85, 3.16], [28.38, 2.76], [24.85, 2.78], [24.86, 2.78], [24.49, 2.99], [23.48, 5.56], [21.99, 9.47], [20.91, 12.14], [19.23, 16.49], [17.68, 20.47], [17.68, 20.46], [17.63, 20.6], [17.61, 20.62], [17.38, 21.18], [17.37, 21.21], [17.16, 21.7], [16.88, 21.29], [15.46, 19.09], [13.53, 16.09], [11.59, 13.12], [9.91, 10.48], [9.92, 10.48], [9.88, 10.36], [9.88, 10.29], [9.97, 10.01], [14.73, 5.05], [16.55, 3.17], [16.55, 3.17], [16.76, 2.84], [16.77, 2.82], [16.25, 2.8], [12.03, 2.82], [11.36, 3.12], [7.57, 7.24], [3.98, 11.15], [3.98, 10.73], [3.98, 3.3], [3.98, 3.18], [3.51, 2.81], [0.54, 2.81], [0.02, 3.23], [0.02, 21.39], [0, 21.51], [0, 21.51], [0.02, 21.56], [0.32, 21.86], [3.95, 21.88], [3.95, 21.3], [3.96, 16.49], [3.96, 16.4], [3.96, 16.4], [4.21, 15.88], [6.65, 13.36], [7.28, 13.45], [10.16, 18.37], [11.94, 21.46], [12.62, 21.86], [20.91, 21.86], [20.91, 21.86], [21.47, 21.48], [22.8, 17.81], [22.8, 17.81], [23.23, 17.5], [30.04, 17.51], [30.46, 17.79], [30.46, 17.79], [30.67, 18.3], [31.89, 21.53], [31.89, 21.53], [32.4, 21.86], [35.89, 21.86], [35.89, 21.86], [36.27, 21.81], [35.59, 20.1]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [-0.84, 2.32], [0, 0], [-0.62, -1.68], [0, 0], [0, 0]], "o": [[0.82, -2.23], [0, 0], [0.64, 1.75], [0, 0], [0, 0], [0, 0]], "v": [[24.08, 14.25], [26.54, 7.47], [26.55, 7.46], [28.43, 12.56], [29.06, 14.24], [24.09, 14.24]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [0.58, 0.63, 0.67, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "gr", "hd": false, "it": [{"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[41.94, 21.84], [41.94, 2.79], [38, 2.79], [38, 21.84]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [1, 1, 1, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "gr", "hd": false, "it": [{"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.82, 1.29], [0, 0], [1.4, 0.96], [0.05, 0.04], [-0.16, 0.06], [-0.37, 0.14], [-0.49, 1.32], [0, 0], [-0.07, 0.48], [0.41, 0.91], [1.46, 0.32], [1.21, 0], [2.62, 0], [0, 0], [0.17, -0.02], [0, 0], [0, -0.09], [0, 0], [-0.32, 0], [-1.01, -0.02], [-0.09, 0.02], [0, 0.28], [0, 2.27], [0, 0], [-0.52, -0.02], [0, 0], [-0.51, -0.54], [-0.37, -0.52], [-1.19, -1.75], [-0.19, -0.02], [-1.34, 0], [0, 0], [-0.09, 0.03], [0, 0], [0.13, 0.19]], "o": [[-0.8, -1.29], [0, 0], [-0.83, -1.48], [-0.05, -0.04], [0.14, -0.09], [0.37, -0.11], [1.32, -0.47], [0, 0], [0.16, -0.45], [0.12, -1], [-0.52, -1.4], [-1.19, -0.23], [-2.62, -0.05], [0, 0], [-0.17, 0.01], [0, 0], [0, 0.12], [0, 0], [0, 0.35], [1.01, -0.02], [0.09, 0.01], [0.18, -0.05], [-0.02, -2.29], [0, 0], [0.57, 0], [0, 0], [0.75, -0.02], [0.43, 0.48], [1.19, 1.75], [0.11, 0.15], [1.34, 0.02], [0, 0], [0.1, 0], [0, 0], [-0.09, -0.21], [0, 0]], "v": [[62.27, 21.21], [59.86, 17.36], [59.87, 17.36], [56.48, 13.66], [56.32, 13.57], [56.78, 13.34], [57.89, 13.03], [60.75, 10.22], [60.75, 10.2], [61.1, 8.8], [60.66, 5.89], [57.49, 3.13], [53.88, 2.78], [46.02, 2.77], [46.02, 2.77], [45.52, 2.82], [45.52, 2.83], [45.5, 3.11], [45.5, 21.44], [45.93, 21.86], [48.99, 21.86], [49.25, 21.84], [49.46, 21.36], [49.46, 14.52], [49.46, 13.99], [51.08, 13.99], [51.08, 13.99], [53.05, 14.81], [54.26, 16.31], [57.8, 21.58], [58.27, 21.84], [62.29, 21.86], [62.29, 21.86], [62.59, 21.8], [62.61, 21.8], [62.27, 21.21]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0.02, 0], [0.02, 0], [-0.02, 1.57], [0, 0], [-0.09, 0], [0, 0], [-1.64, -0.04], [0, 0], [-0.26, -0.84], [1.7, -0.12], [0.35, -0.02], [1.5, -0.05]], "o": [[0, 0], [-0.02, 0], [-0.02, 0], [0, -1.52], [0, 0], [0.09, -0.02], [0, 0], [1.64, 0], [0, 0], [0.88, -0.04], [0.4, 1.29], [-0.35, 0.02], [-1.51, 0.03], [0, 0]], "v": [[49.6, 10.81], [49.57, 10.81], [49.52, 10.8], [49.46, 10.78], [49.48, 6.17], [49.48, 6.17], [49.74, 6.13], [49.81, 6.13], [54.73, 6.17], [54.91, 6.17], [56.84, 7.53], [55.18, 10.66], [54.13, 10.73], [49.6, 10.81]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [1, 1, 1, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "gr", "hd": false, "it": [{"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [-0.04, -0.11], [-1.88, -1.05], [-1.35, -0.2], [-1.88, 0.6], [-1.11, 0.98], [-0.42, 2.51], [0.23, 1.28], [0, 0], [-1.69, 0.02], [-3.32, 0], [0, 0], [0.01, 0.07], [0.46, 0.62], [2.93, 0.34], [1.68, -0.52], [1.14, -0.95], [0.52, -2.31], [-0.21, -1.37], [-0.33, 0], [0, 0], [0.23, -0.24], [0, 0], [1.35, 0.03], [0, 0]], "o": [[0.02, 0.12], [0.96, 1.93], [1.18, 0.68], [1.94, 0.35], [1.41, -0.46], [1.97, -1.61], [0.2, -1.29], [0, 0], [1.01, -1.48], [3.33, -0.05], [0, 0], [0.01, -0.07], [-0.37, -0.68], [-1.84, -2.31], [-1.74, -0.25], [-1.41, 0.45], [-1.87, 1.46], [-0.31, 1.35], [0.07, 0.44], [0, 0], [-0.28, 0.37], [0, 0], [-0.91, 1], [0, 0], [0, 0]], "v": [[86.99, 15.42], [87.07, 15.77], [91.44, 20.34], [95.26, 21.69], [101.07, 21.3], [104.9, 19.12], [108.6, 12.73], [108.55, 8.86], [93.15, 8.86], [97.11, 6.45], [107.09, 6.43], [107.6, 6.43], [107.6, 6.21], [106.36, 4.25], [98.91, 0.12], [93.73, 0.52], [89.87, 2.63], [86.17, 8.46], [86.01, 12.58], [86.61, 13.04], [101.46, 13.04], [100.78, 13.91], [100.77, 13.91], [97.21, 15.43], [86.99, 15.43]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [1, 1, 1, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "gr", "hd": false, "it": [{"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.21, 0.56], [0.65, 1.61], [0.96, 2.39], [0.64, 1.64], [0.32, -0.02], [1.17, -0.02], [0, 0], [0.08, -0.12], [0.33, -0.86], [0.51, -1.29], [0.35, -0.89], [0, 0], [0.52, -1.33], [0, 0], [0.01, -0.05], [0.01, 0], [0.09, -0.19], [0, -0.02], [0.07, -0.2], [0, 0], [0, 0], [0.64, 0.99], [0.64, 1], [0.54, 0.89], [0, 0.05], [0, 0], [-0.06, 0.07], [-1.59, 1.64], [-0.59, 0.62], [-0.11, 0.16], [0, 0], [0.14, 0], [1.4, -0.04], [0, 0], [0.18, -0.18], [1.26, -1.38], [1.22, -1.33], [0, 0], [0.33, 0], [1, 0.04], [0.04, -0.3], [0, 0], [0, -6], [0, 0], [0, -0.04], [-0.02, -0.02], [-0.11, 0], [0, 0], [0, 0], [-0.02, 1.61], [0, 0], [0, 0], [-0.14, 0.14], [-0.82, 0.84], [-0.23, -0.39], [0, 0], [0, 0], [0, 0], [-0.29, 0.02], [-2.76, -0.02], [-0.05, 0.26], [-0.42, 1.22], [-0.2, -0.03], [-2.27, -0.02], [-0.09, -0.14], [-0.05, -0.18], [-0.39, -1.08], [-0.26, 0], [0, 0], [0, 0], [-0.12, 0.04]], "o": [[-0.25, -0.61], [-0.64, -1.61], [-0.96, -2.39], [-0.64, -1.64], [-0.09, -0.19], [-1.17, 0.02], [0, 0], [-0.14, 0.01], [-0.35, 0.86], [-0.49, 1.31], [-0.35, 0.89], [0, 0], [-0.51, 1.33], [0, 0], [-0.02, 0.05], [0, 0.01], [-0.07, 0.18], [0, 0.02], [-0.07, 0.16], [0, 0], [0, 0], [-0.64, -1], [-0.64, -0.99], [-0.56, -0.87], [-0.02, -0.04], [0, 0], [0, -0.09], [1.59, -1.66], [0.61, -0.63], [0.07, -0.09], [0, 0], [-0.23, -0.02], [-1.4, -0.04], [0, 0], [-0.25, 0.01], [-1.27, 1.36], [-1.19, 1.29], [0, 0], [-0.02, -0.26], [-1, 0.02], [-0.38, -0.02], [0, 0], [0.02, 6], [0, 0], [0, 0.04], [0, 0.04], [0.09, 0.12], [0, 0], [0, 0], [0, -1.61], [0, 0], [0, 0], [0.02, -0.19], [0.8, -0.86], [0.31, -0.31], [0, 0], [0, 0], [0, 0], [0.12, 0.27], [2.76, -0.02], [0.26, 0.04], [0.44, -1.22], [0.04, -0.2], [2.27, 0], [0.17, 0.03], [0.09, 0.16], [0.42, 1.08], [0.11, 0.3], [0, 0], [0, 0], [0.12, -0.01], [0, 0]], "v": [[36.27, 21.81], [35.59, 20.12], [33.67, 15.32], [30.79, 8.12], [28.85, 3.18], [28.38, 2.78], [24.86, 2.8], [24.85, 2.8], [24.49, 3.01], [23.48, 5.57], [21.99, 9.48], [20.91, 12.16], [19.23, 16.5], [17.68, 20.48], [17.68, 20.48], [17.63, 20.62], [17.61, 20.64], [17.38, 21.2], [17.37, 21.23], [17.16, 21.74], [16.88, 21.32], [15.46, 19.12], [13.53, 16.12], [11.59, 13.15], [9.91, 10.52], [9.88, 10.38], [9.88, 10.31], [9.97, 10.05], [14.73, 5.09], [16.55, 3.21], [16.78, 2.86], [16.76, 2.86], [16.25, 2.84], [12.05, 2.83], [12.04, 2.82], [11.38, 3.12], [7.59, 7.24], [4, 11.15], [4, 3.15], [3.52, 2.8], [0.55, 2.8], [0.03, 3.21], [0.03, 3.35], [0.03, 21.34], [0.03, 21.38], [0.02, 21.5], [0.03, 21.57], [0.33, 21.87], [3.96, 21.87], [3.96, 21.31], [3.98, 16.5], [3.98, 16.41], [3.98, 16.4], [4.23, 15.89], [6.67, 13.38], [7.3, 13.47], [10.18, 18.39], [11.96, 21.48], [11.96, 21.48], [12.64, 21.88], [20.93, 21.88], [21.49, 21.5], [22.82, 17.83], [23.25, 17.52], [30.06, 17.54], [30.48, 17.81], [30.69, 18.32], [31.91, 21.55], [32.42, 21.88], [35.91, 21.88], [35.91, 21.88], [36.27, 21.81]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [-0.84, 2.32], [-0.62, -1.68]], "o": [[0, 0], [0, 0], [0.82, -2.23], [0.64, 1.75], [0, 0]], "v": [[28.43, 12.56], [29.06, 14.23], [24.09, 14.23], [26.55, 7.46], [28.43, 12.56]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [1, 1, 1, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "gr", "hd": false, "it": [{"ty": "gr", "hd": false, "it": [{"ty": "sh", "ks": {"k": {"i": [[0, 0], [1.69, -4.08], [-3.12, -3.12], [-4.08, 1.69], [0, 4.41], [6.02, 0]], "o": [[-4.41, 0], [-1.69, 4.07], [3.12, 3.12], [4.07, -1.69], [0, -6.02], [0, 0]], "v": [[73.51, 0.04], [63.43, 6.78], [65.8, 18.66], [77.69, 21.03], [84.42, 10.95], [73.51, 0.04]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.34, -0.36], [0.6, -0.25], [1.98, 0.57], [0.7, 0.61], [0.04, 0.55], [0, 0.72], [0, 0], [0, 0], [-1.71, -0.66], [0, 0], [-0.18, 0.09], [-1.71, 0.68], [0, 0], [-0.08, 0.01]], "o": [[0.01, 0.5], [-0.43, 0.48], [-1.89, 0.81], [-0.91, -0.21], [-0.43, -0.35], [-0.02, -0.71], [0, 0], [0, 0], [1.73, 0.66], [0, 0], [0.18, 0.09], [1.71, -0.66], [0, 0], [0.07, -0.04], [0, 0]], "v": [[79.32, 12.42], [78.8, 13.76], [77.23, 14.86], [71.26, 15.25], [68.8, 13.99], [68.07, 12.57], [68.07, 10.43], [68.08, 10.41], [68.24, 10.46], [73.4, 12.47], [73.41, 12.47], [73.98, 12.47], [79.09, 10.46], [79.1, 10.46], [79.32, 10.39]], "c": true}}}, {"ty": "sh", "ks": {"k": {"i": [[0, 0], [0.19, 0.09], [2.36, 0.91], [0.05, 0.03], [0.01, -0.03], [0, 0], [-0.02, -0.04], [0, -0.25], [0.25, -0.1], [-0.04, -0.51], [0.23, -0.12], [0.07, 0], [0, 0], [0, 0], [0.03, 0.26], [0, 0], [0, 0.05], [0, 0], [-0.07, 0.26], [-0.05, 0.14], [0, 0.27], [-0.24, 0.11], [0, 0.04], [0, 0], [0, 0.07], [0, 0], [-0.04, 0.05], [-0.05, 0.01], [-2.54, 0.89], [-0.2, -0.07], [-2.55, -0.91], [0, 0], [-0.07, -0.06], [0.08, -0.05], [2.55, -1]], "o": [[-0.2, 0.09], [-2.36, -0.93], [-0.06, -0.02], [0, 0.03], [0, 0], [0, 0.04], [0.22, 0.12], [0, 0.27], [0.19, 0.48], [0.03, 0.26], [-0.07, 0.04], [0, 0], [0, 0], [-0.26, -0.01], [0, 0], [0, -0.04], [0, 0], [0.05, -0.26], [0.05, -0.19], [-0.24, -0.11], [0, -0.27], [0.02, -0.04], [0, 0], [0, -0.07], [0, 0], [0.01, -0.07], [0.05, -0.04], [2.55, -0.89], [0.2, -0.07], [2.55, 0.91], [0, 0], [0.08, 0.04], [-0.07, 0.07], [-2.54, 1.03], [0, 0]], "v": [[74, 11.89], [73.39, 11.89], [66.3, 9.14], [66.13, 9.07], [66.11, 9.15], [66.11, 12.61], [66.14, 12.7], [66.5, 13.29], [66.09, 13.9], [66.44, 15.4], [66.09, 16.03], [65.88, 16.08], [65.71, 16.08], [65.71, 16.08], [65.2, 15.61], [65.2, 15.3], [65.22, 15.18], [65.22, 15.16], [65.39, 14.39], [65.55, 13.88], [65.15, 13.26], [65.55, 12.64], [65.57, 12.54], [65.57, 9], [65.52, 8.77], [65.52, 8.77], [65.59, 8.6], [65.74, 8.53], [73.39, 5.84], [74, 5.84], [81.66, 8.55], [81.66, 8.54], [81.89, 8.7], [81.66, 8.88], [74, 11.89]], "c": true}}}, {"ty": "fl", "o": {"k": 100}, "c": {"k": [0.03, 0.7, 0.87, 1]}}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}, {"ty": "tr", "o": {"k": 100}, "r": {"k": 0}, "p": {"k": [0, 0]}, "a": {"k": [0, 0]}, "s": {"k": [100, 100]}, "sk": {"k": 0}, "sa": {"k": 0}, "hd": false}]}]}], "meta": {"g": "LF SVG to Lottie"}, "assets": []}