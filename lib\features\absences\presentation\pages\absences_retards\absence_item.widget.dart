import 'package:Kairos/features/absences/domain/entities/absence_retard_entity.dart';
import 'package:flutter/material.dart';

class AbsenceItem extends StatelessWidget {
  final AbsenceRetardEntity absenceRetard;

  const AbsenceItem({
    super.key,
    required this.absenceRetard,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 7.0, vertical: 4.0),
      height: 55,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Theme.of(context).colorScheme.secondary, width: 1),
        // borderRadius: BorderRadius.circular(5.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Left colored indicator with type badge
          Container(
            width: 55,
            decoration: BoxDecoration(
              color: absenceRetard.type == 'R' ? const Color(0xFFF77000) : const Color(0xFF920000),
              
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Type letter (R or A)
                Text(
                  absenceRetard.type,
                  style: const TextStyle(

                    fontSize: 20,
                    fontWeight: FontWeight.w400,
                    color: Colors.white,
                    height: 1.21,
                  ),
                ),
                // Duration for tardiness
                if (absenceRetard.type == 'R' && absenceRetard.formattedDuration != null)
                  Text(
                    absenceRetard.formattedDuration!,
                    style: const TextStyle(
  
                      fontSize: 9,
                      fontWeight: FontWeight.w400,
                      color: Colors.white,
                      height: 1.21,
                    ),
                  ),
              ],
            ),
          ),
          // Main content area
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 6.0, right: 16.0, top: 6.0, bottom: 6.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Top row: Date recorded
                  Row(
                    children: [
                      Text(
                        'Enregistré le ${absenceRetard.dateEnregistrement}',
                        style: const TextStyle(
      
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                          height: 1.21,
                        ),
                      ),
                      const Spacer(),
                      // Justification status
                      Text(
                        absenceRetard.justifie
                            ? 'Justifié${absenceRetard.dateJustification != null ? ' le ${absenceRetard.dateJustification}' : ''}'
                            : 'Non justifiée',
                        style: TextStyle(
      
                          fontSize: 10,
                          fontWeight: FontWeight.w400,
                          color: absenceRetard.justifie ? const Color(0xFF055A28) : const Color(0xFF920000),
                          height: 1.21,
                        ),
                      ),
                    ],
                  ),
                  // Middle row: Course name
                  Text(
                    absenceRetard.cours,
                    style: const TextStyle(
  
                      fontSize: 10,
                      fontWeight: FontWeight.w700,
                      color: Colors.black,
                      height: 1.21,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  // Bottom row: Teacher and class info
                  Text(
                    'Avec ${absenceRetard.professeur} EN ${absenceRetard.classe}',
                    style: TextStyle(
  
                      fontSize: 9,
                      fontWeight: FontWeight.w400,
                      color: Colors.black.withValues(alpha: 0.5),
                      height: 1.21,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}