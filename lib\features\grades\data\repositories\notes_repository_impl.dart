import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/notes_evaluation_entity.dart';
import '../../domain/repositories/notes_repository.dart';
import '../datasources/notes_remote_datasource.dart';

/// Implementation of NotesRepository
class NotesRepositoryImpl implements NotesRepository {
  final NotesRemoteDataSource remoteDataSource;

  NotesRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<NotesEvaluationEntity>>> getNotesEvaluations({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      final notesModels = await remoteDataSource.getNotesEvaluations(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );

      // Convert models to entities
      final notesEntities = notesModels.map((model) => model.toEntity()).toList();
      
      return Right(notesEntities);
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return Left(AuthenticationFailure('Authentication failed'));
      } else if (e.response?.statusCode == 404) {
        return Left(NotFoundFailure('Notes not found'));
      } else if (e.response?.statusCode == 500) {
        return Left(ServerFailure('Server error occurred'));
      } else {
        return Left(NetworkFailure('Network error: ${e.message}'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure('Unexpected error: $e'));
    }
  }
}
