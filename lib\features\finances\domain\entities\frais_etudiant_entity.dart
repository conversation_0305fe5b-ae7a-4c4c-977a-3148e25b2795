import 'package:Kairos/features/finances/data/models/frais_etudiant_model.dart';
import 'package:equatable/equatable.dart';

/// Entity for student fee information
class FraisEtudiantEntity extends Equatable {
  final String intituleFrais;
  final String? moisConcerne;
  final num? montantTotalFrais;
  final num? montantFrais;
  final String? dateEchance;
  final String? dateEchanceFrais;
  final String? numeroQuittance;
  final String? dateQuittance;
  final bool? indicateur;
  final num? montantEncaisseAjour;
  final String? dateDernierPaiement;

  const FraisEtudiantEntity({
    required this.intituleFrais,
    this.moisConcerne,
    this.montantTotalFrais,
    this.montantFrais,
    this.dateEchance,
    this.dateEchanceFrais,
    this.numeroQuittance,
    this.dateQuittance,
    this.indicateur,
    this.montantEncaisseAjour,
    this.dateDernierPaiement,
  });

  @override
  List<Object?> get props => [
        intituleFrais,
        moisConcerne,
        montantTotalFrais,
        montantFrais,
        dateEchance,
        dateEchanceFrais,
        numeroQuittance,
        dateQuittance,
        indicateur,
        montantEncaisseAjour,
        dateDernierPaiement,
      ];


  static fromModel(FraisEtudiantModel model) {
    return FraisEtudiantEntity(
      intituleFrais: model.intituleFrais,
      moisConcerne: model.moisConcerne,
      montantTotalFrais: model.montantTotalFrais,
      montantFrais: model.montantFrais,
      dateEchance: model.dateEchance,
      dateEchanceFrais: model.dateEchanceFrais,
      numeroQuittance: model.numeroQuittance,
      dateQuittance: model.dateQuittance,
      indicateur: model.indicateur,
      montantEncaisseAjour: model.montantEncaisseAjour,
      dateDernierPaiement: model.dateDernierPaiement,
    );
  }
}
