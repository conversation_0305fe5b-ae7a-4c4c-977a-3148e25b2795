import 'package:equatable/equatable.dart';

/// Profile entity representing user profile data in the domain layer
class ProfileEntity extends Equatable {
  final String id;
  final String codeUtilisateur;
  final String profil;
  final String photo;
  final bool? isAuthenticated;
  final String? message;
  final String fullName;
  final String phoneNumber;
  final String schoolName;
  final String schoolCode;
  final String schoolLogo;

  const ProfileEntity({
    required this.id,
    required this.codeUtilisateur,
    required this.profil,
    required this.photo,
    this.isAuthenticated,
    this.message,
    required this.fullName,
    required this.phoneNumber,
    required this.schoolName,
    required this.schoolCode,
    required this.schoolLogo,
  });

  @override
  List<Object?> get props => [
        id,
        codeUtilisateur,
        profil,
        photo,
        isAuthenticated,
        message,
        fullName,
        phoneNumber,
        schoolName,
        schoolCode,
        schoolLogo,
      ];
}
