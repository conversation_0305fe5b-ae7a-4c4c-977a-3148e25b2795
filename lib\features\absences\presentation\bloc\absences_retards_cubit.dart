import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/get_absences_retards_usecase.dart';
import '../../domain/usecases/get_absences_retards_filtres_usecase.dart';
import 'absences_retards_state.dart';

/// Absences Retards Cubit for managing absences and tardiness state
class AbsencesRetardsCubit extends Cubit<AbsencesRetardsState> {
  final GetAbsencesRetardsUseCase getAbsencesRetardsUseCase;
  final GetAbsencesRetardsFiltresUseCase getAbsencesRetardsFiltresUseCase;
  
  AbsencesRetardsCubit({
    required this.getAbsencesRetardsUseCase,
    required this.getAbsencesRetardsFiltresUseCase,
  }) : super(const AbsencesRetardsInitial());
  
  /// Load absences and tardiness data
  Future<void> loadAbsencesRetards({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(const AbsencesRetardsLoading());
    
    try {
      final params = GetAbsencesRetardsParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );
      
      final result = await getAbsencesRetardsUseCase.call(params);
      debugPrint('AbsencesRetardsCubit: loadAbsencesRetards result: $result');
      result.fold(
        (failure) => emit(AbsencesRetardsError(failure.message)),
        (absencesRetards)  {
          debugPrint('AbsencesRetardsCubit: AbsencesRetardsLoaded --> absencesRetards: $absencesRetards');
          emit(AbsencesRetardsLoaded(absencesRetards: absencesRetards),
          );
        },
      );
    } catch (e) {
      emit(AbsencesRetardsError('Une erreur inattendue s\'est produite: $e'));
    }
  }

  /// Load filtered absences and tardiness data
  Future<void> loadAbsencesRetardsFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  }) async {
    emit(const AbsencesRetardsLoading());
    
    try {
      final params = GetAbsencesRetardsFiltresParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
        startDate: startDate,
        endDate: endDate,
      );
      
      final result = await getAbsencesRetardsFiltresUseCase.call(params);
      
      result.fold(
        (failure) => emit(AbsencesRetardsError(failure.message)),
        (absencesRetards) => emit(AbsencesRetardsLoaded(absencesRetards: absencesRetards)),
      );
    } catch (e) {
      emit(AbsencesRetardsError('Une erreur inattendue s\'est produite: $e'));
    }
  }
  
  /// Refresh absences retards data
  Future<void> refresh({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    await loadAbsencesRetards(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
      codeUtilisateur: codeUtilisateur,
    );
  }
}
