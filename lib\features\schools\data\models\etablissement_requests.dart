// Models for etablissement-related requests
class EtablissementCodeRequest {
  final String code;

  EtablissementCodeRequest({required this.code});

  factory EtablissementCodeRequest.fromJson(Map<String, dynamic> json) {
    return EtablissementCodeRequest(
      code: json['code'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'code': code};
  }
}

class CheckEtablissementsRequest {
    final String numeroTelephone;

    CheckEtablissementsRequest({required this.numeroTelephone});

    factory CheckEtablissementsRequest.fromJson(Map<String, dynamic> json) {
        return CheckEtablissementsRequest(
            numeroTelephone: json['numeroTelephone'],
        );
    }

    Map<String, dynamic> toJson() {
        return {
            'numeroTelephone': numeroTelephone,
        };
    }
}
