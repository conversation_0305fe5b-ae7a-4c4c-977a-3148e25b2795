import 'package:equatable/equatable.dart';

/// User entity representing a user in the domain layer
class User extends Equatable {
  final String id;
  final String fullName;
  final String? phoneNumber;
  final String? profileImageUrl;
  final DateTime? lastLoginAt;
  
  const User({
    required this.id,
    required this.fullName,
    this.phoneNumber,
    this.profileImageUrl,
    this.lastLoginAt,
  });
  
  @override
  List<Object?> get props => [
    id,
    fullName,
    phoneNumber,
    profileImageUrl,
    lastLoginAt,
  ];
}
