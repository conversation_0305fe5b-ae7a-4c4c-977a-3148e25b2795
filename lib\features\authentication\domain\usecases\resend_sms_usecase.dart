import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/auth_repository.dart';

/// Use case for resending SMS verification code
class ResendSmsUseCase {
  final AuthRepository repository;

  ResendSmsUseCase(this.repository);

  /// Execute the resend SMS use case
  ///
  /// [phoneNumber] - The phone number to resend SMS to
  /// Returns [Either<Failure, void>] - Success or failure result
  Future<Either<Failure, void>> call(String phoneNumber) async {
    return await repository.resendSms(phoneNumber);
  }
}