import 'package:Kairos/features/dashboard/data/dashboard_item_type.enum.dart';
import 'package:Kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/features/dashboard/presentation/pages/dashboard/dashboard_item.widget.dart';
import 'package:Kairos/core/widgets/common/hero_widget.dart';
import 'package:Kairos/core/constants/dashboard_strings.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/core/di/injection_container.dart';
import 'package:Kairos/features/dashboard/presentation/bloc/dashboard_cubit.dart';
import 'package:Kairos/features/dashboard/presentation/bloc/dashboard_state.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/features/dashboard/domain/entities/dashboard_entity.dart';
import 'package:flutter_svg/svg.dart';


class Dashboard extends StatefulWidget {
  const Dashboard({
    super.key,
    this.userName,
  });

  final String? userName;

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  int _currentIndex = 1;
  EtablissementUtilisateur? _school;
  EnfantTuteurEntity? _etudiant;
  bool _isLoading = true;

  /// Generate dynamic dashboard items based on DashboardEntity data
  List<Map<String, dynamic>> _generateDashboardItems(DashboardEntity? dashboardData) {
    if (dashboardData == null) {
      // Return static items if no data is available
      return [
        {"title": DashboardStrings.notesTitle, "icon": "icone_notes.svg", "itemType": DashboardItemType.notes},
        {"title": DashboardStrings.absencesTitle, "icon": "icone_absences.svg", "itemType": DashboardItemType.absences},
        {"title": DashboardStrings.dossiersTitle, "icon": "icone_dossier_etudiant.svg", "itemType": DashboardItemType.dossiers},
        {"title": DashboardStrings.financesTitle, "icon": "icone_finance.svg", "itemType": DashboardItemType.finances},
        {"title": DashboardStrings.cahierTitle, "icon": "icone_cahier_de_texte.svg", "itemType": DashboardItemType.cahierTexte},
        {"title": DashboardStrings.planningTitle, "icon": "icone_planning_cours.svg", "itemType": DashboardItemType.planning},
        {"title": DashboardStrings.availableRessources, "icon": "icone_ressources_telechargeable.svg", "itemType": DashboardItemType.ressources},
      ];
    }

    return [
      {
        "title": Text.rich(TextSpan(children: [
          TextSpan(text: dashboardData.note.nombreNote.toString(), style: const TextStyle(fontWeight: FontWeight.bold)),
          const TextSpan(text: " notes enregistrées pour "),
          TextSpan(text: dashboardData.anneeScolaire, style: const TextStyle(fontWeight: FontWeight.bold)),
        ])),
        "icon": "icone_notes.svg",
        "itemType": DashboardItemType.notes
      },
      {
        "title": Text.rich(TextSpan(children: [
          TextSpan(text: dashboardData.assiduite.nombreAbsence.toString(), style: const TextStyle(fontWeight: FontWeight.bold)),
          const TextSpan(text: " absence(s) et "),
          TextSpan(text: dashboardData.assiduite.nombreRetard.toString(), style: const TextStyle(fontWeight: FontWeight.bold)),
          const TextSpan(text: " retard(s) enregistrés pour "),
          TextSpan(text: dashboardData.anneeScolaire, style: const TextStyle(fontWeight: FontWeight.bold)),
        ])),
        "icon": "icone_absences.svg",
        "itemType": DashboardItemType.absences
      },
      {
        "title": Text.rich(TextSpan(children: [
          TextSpan(text: dashboardData.dossier.nombreElement.toString(), style: const TextStyle(fontWeight: FontWeight.bold)),
          const TextSpan(text: " éléments ajoutés dans votre dossier pour "),
          TextSpan(text: dashboardData.anneeScolaire, style: const TextStyle(fontWeight: FontWeight.bold)),
        ])),
        "icon": "icone_dossier_etudiant.svg",
        "itemType": DashboardItemType.dossiers
      },
      {
        "title": Text.rich(TextSpan(children: [
          TextSpan(text: dashboardData.etatFinancier.montantEncaisse.toStringAsFixed(0), style: const TextStyle(fontWeight: FontWeight.bold)),
          const TextSpan(text: " XOF encaissé sur "),
          TextSpan(text: dashboardData.etatFinancier.montantAencaisser.toStringAsFixed(0), style: const TextStyle(fontWeight: FontWeight.bold)),
          const TextSpan(text: " pour "),
          TextSpan(text: dashboardData.anneeScolaire, style: const TextStyle(fontWeight: FontWeight.bold)),
        ])),
        "subtitle": "Total impayé: ${dashboardData.etatFinancier.montantImpaye.toStringAsFixed(0)}",
        "icon": "icone_finance.svg",
        "itemType": DashboardItemType.finances
      },
      {
        "title": Text.rich(TextSpan(children: [
          TextSpan(text: dashboardData.cahierTexte.nombreEntre.toString(), style: const TextStyle(fontWeight: FontWeight.bold)),
          const TextSpan(text: " entrées de cahier de texte saisies pour "),
          TextSpan(text: dashboardData.anneeScolaire, style: const TextStyle(fontWeight: FontWeight.bold)),
        ])),
        "subtitle": "Semestre ${dashboardData.cahierTexte.semestre}",
        "icon": "icone_cahier_de_texte.svg",
        "itemType": DashboardItemType.cahierTexte
      },
      {
        "title": Text.rich(TextSpan(children: [
          TextSpan(text: dashboardData.plannings.nombrePlanningSemaine.toString(), style: const TextStyle(fontWeight: FontWeight.bold)),
          const TextSpan(text: " plannings pour la semaine dont "),
          TextSpan(text: dashboardData.plannings.nombrePlanningJour.toString(), style: const TextStyle(fontWeight: FontWeight.bold)),
          const TextSpan(text: " pour la journée"),
        ])),
        "icon": "icone_planning_cours.svg",
        "itemType": DashboardItemType.planning
      },
      {
        "title": Text.rich(TextSpan(children: [
          TextSpan(text: dashboardData.ressourcesPedagogiques.nombreRessource.toString(), style: const TextStyle(fontWeight: FontWeight.bold)),
          const TextSpan(text: " ressources pédagogiques disponibles"),
        ])),
        "icon": "icone_ressources_telechargeable.svg",
        "itemType": DashboardItemType.ressources
      },
    ];
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDashboardData();
    });
  }

  Future<void> _loadDashboardData() async {
    try {
      // Get data from route arguments
      final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;

      if (args != null) {
        final currentSchool = args['school'] as EtablissementUtilisateur;
        setState(() {
          _school = currentSchool;
        });

        // Get phone number from SharedPreferences
        final authLocalDataSource = sl<AuthLocalDataSource>();
        final phoneNumber = await authLocalDataSource.getPhoneNumber();

        if (phoneNumber != null && mounted) {
          // Check if this is a parent/tutor workflow (etudiant key exists)
          if (args.containsKey('etudiant')) {
            // Parent/tutor workflow - load data for selected child
            _etudiant = args['etudiant'] ;
            context.read<DashboardCubit>().loadDashboardData(
              codeEtab: _school!.codeEtab,
              telephone: phoneNumber,
              codeEtudiant: _etudiant!.codeEtudiant,
              codeUtilisateur: _school!.codeUtilisateur,
            );
          } else {
            // ETU (student) workflow - load data for current user
            context.read<DashboardCubit>().loadDashboardData(
              codeEtab: _school!.codeEtab,
              telephone: phoneNumber,
              codeEtudiant: _school!.codeUtilisateur,
            );
          }
        } else {
          setState(() {
            _isLoading = false;
          });
          // Handle case where phone number is not available
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text("Erreur: Numéro de téléphone non disponible")),
            );
          }
        }
      } else {
        setState(() {
          _isLoading = false;
        });
        // Handle case where school data is not available
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Erreur: Données de l'école non disponibles")),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      debugPrint('Error loading dashboard data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erreur lors du chargement des données")),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: BlocListener<DashboardCubit, DashboardState>(
        listener: (context, state) {
          if (state is DashboardLoaded) {
            setState(() {
              _isLoading = false;
            });
          } else if (state is DashboardError) {
            setState(() {
              _isLoading = false;
            });
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text("Erreur: ${state.message}")),
              );
            }
          }
        },
        child: BlocBuilder<DashboardCubit, DashboardState>(
          builder: (context, state) {
            if (state is DashboardLoading || _isLoading) {
              return Center(
                child: CustomSpinner(
                  size: 60.0,
                  strokeWidth: 5.0,
                ),
              );
            }

            // Get dashboard data from state if available
            DashboardEntity? dashboardData;
            if (state is DashboardLoaded) {
              dashboardData = state.dashboardData;
            }

            // Generate dashboard items based on loaded data
            final dashboardItems = _generateDashboardItems(dashboardData);

            return SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                mainAxisSize: MainAxisSize.min,
                children: [
                  HeroWidget(enfantDuTuteur: _etudiant, etablissementUser: _school),
                  SizedBox(height: 20),
                  Text(DashboardStrings.dashboardTitle, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                  Divider(color: Theme.of(context).primaryColor, thickness: 4, height: 20, indent: 150, endIndent: 150,),
                  ...dashboardItems.map((item) => DashboardItem(
                    title: item["title"]!,
                    subtitle: item["subtitle"],
                    iconName: item["icon"]!,
                    itemType: item["itemType"],
                    school: _school,
                    etudiant: _etudiant,
                  )),

                ]),
            );
          },
        ),
      ),
      // Use a Stack to overlay the FAB on the BottomNavigationBar
      bottomNavigationBar: Stack(
        alignment: Alignment.bottomCenter,
        clipBehavior: Clip.none,
        children: [
          BottomNavigationBar(
            onTap: (index) {
              setState(() => _currentIndex = index);
              if (index == 0) {
                debugPrint("user wants to view his profile");
                Navigator.pushNamed(context, "/profile");
              } else if (index == 1) {
                debugPrint("user wants to view his notifications");
                Navigator.pushNamed(context, "/notifications");
              }
            },
            backgroundColor: Colors.black,
            selectedItemColor: Theme.of(context).primaryColor,
            currentIndex: _currentIndex == 2 ? 0 : _currentIndex, // Adjust index for 2 items
            unselectedItemColor: Colors.white,
            items:  [
              BottomNavigationBarItem(icon: SvgPicture.asset('assets/icons/icone_profil_menu.svg'), label: ""),
              BottomNavigationBarItem(icon: SvgPicture.asset('assets/icons/icone_notification_menu.svg'), label: ""),
            ],
          ),
          // Centered FloatingActionButton
          Positioned(
            bottom: 14, // Adjust as needed for overlap
            child: SizedBox(
              height: 100, // Adjust size as needed
              width: MediaQuery.of(context).size.width,
              child: Center(
                child: FloatingActionButton(
                  onPressed: () {
                    setState(() => _currentIndex = 2);
                    debugPrint("user wants to view his dashboard");
                    Navigator.pushNamed(context, "/dashboard");
                  },
                  backgroundColor: Colors.white,
                  elevation: 10,
                  shape: const CircleBorder(),
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    child: SvgPicture.asset(
                      'assets/icons/icone_bouton_principal_menu.svg', // You may need to convert SVG to PNG or use flutter_svg
                      height: 80,
                      width: 80,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}