import 'package:equatable/equatable.dart';

/// Represents the response data after sending an SMS.
class SmsResponseEntity extends Equatable {
  final String id;
  final String numeroTelephone;
  final String marqueTelephone;
  final String modelTelephone;
  final String imeiTelephone;
  final String numeroSerie;
  final bool deleted;
  final bool activated;
  final int version;
  final DateTime dateCreated;
  final DateTime lastUpdated;

  const SmsResponseEntity({
    required this.id,
    required this.numeroTelephone,
    required this.marqueTelephone,
    required this.modelTelephone,
    required this.imeiTelephone,
    required this.numeroSerie,
    required this.deleted,
    required this.activated,
    required this.version,
    required this.dateCreated,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [
        id,
        numeroTelephone,
        marqueTelephone,
        modelTelephone,
        imeiTelephone,
        numeroSerie,
        deleted,
        activated,
        version,
        dateCreated,
        lastUpdated,
      ];
}