﻿import 'package:equatable/equatable.dart';
import '../../domain/entities/dashboard_entity.dart';

/// Base dashboard state
abstract class DashboardState extends Equatable {
  const DashboardState();
  
  @override
  List<Object?> get props => [];
}

/// Initial dashboard state
class DashboardInitial extends DashboardState {
  const DashboardInitial();
}

/// Loading state during dashboard operations
class DashboardLoading extends DashboardState {
  const DashboardLoading();
}

/// Dashboard data loaded successfully
class DashboardLoaded extends DashboardState {
  final DashboardEntity dashboardData;

  const DashboardLoaded({required this.dashboardData});

  @override
  List<Object?> get props => [dashboardData];
}

/// Dashboard error occurred
class DashboardError extends DashboardState {
  final String message;
  
  const DashboardError(this.message);
  
  @override
  List<Object?> get props => [message];
}
