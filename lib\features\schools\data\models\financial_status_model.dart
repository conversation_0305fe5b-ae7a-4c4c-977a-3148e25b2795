import '../../domain/entities/financial_status_entity.dart';

/// Model for financial status data from API
class FinancialStatusModel {
  final String username;
  final bool enRegle;

  const FinancialStatusModel({
    required this.username,
    required this.enRegle,
  });

  /// Create model from JSON
  factory FinancialStatusModel.fromJson(Map<String, dynamic> json) {
    return FinancialStatusModel(
      username: json['username'] as String,
      enRegle: json['enRegle'] as bool,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'enRegle': enRegle,
    };
  }

  /// Convert model to entity
  FinancialStatusEntity toEntity() {
    return FinancialStatusEntity(
      username: username,
      enRegle: enRegle,
    );
  }
}
