import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/etablissement_entity.dart'; // Assuming an entity for Etablissement
import '../entities/etablissement_utilisateur.dart'; // Import EtablissementUtilisateur
import '../entities/financial_status_entity.dart'; // Import FinancialStatusEntity
import '../../data/models/user_profile_model.dart'; // Import UserProfileModel

/// Abstract repository interface for schools operations
abstract class SchoolsRepository {
  /// Get list of all available schools
  Future<Either<Failure, List<EtablissementEntity>>> getAllAvailableSchools();

  /// Get list of schools associated with the current user
  Future<Either<Failure, List<EtablissementUtilisateur>>> getUserSchools(String phoneNumber);

  /// Get raw user profile models for authentication validation
  Future<Either<Failure, List<UserProfileModel>>> getUserProfileModels(String phoneNumber);

  /// Check financial status for a student
  Future<Either<Failure, FinancialStatusEntity>> checkFinancialStatus({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}