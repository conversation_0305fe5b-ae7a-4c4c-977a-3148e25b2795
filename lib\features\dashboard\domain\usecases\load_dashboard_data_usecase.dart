import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/dashboard_entity.dart';
import '../repositories/dashboard_repository.dart';

/// Parameters for loading dashboard data
class LoadDashboardDataParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  const LoadDashboardDataParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });
}

/// Use case for loading dashboard data
class LoadDashboardDataUseCase {
  final DashboardRepository repository;

  const LoadDashboardDataUseCase(this.repository);

  /// Execute the use case to load dashboard data
  Future<Either<Failure, DashboardEntity>> call(
    LoadDashboardDataParams params,
  ) async {
    return await repository.loadDashboardData(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}
