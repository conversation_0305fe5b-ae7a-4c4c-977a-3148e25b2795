import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/finances_repository.dart';
import '../entities/finances_response_entity.dart'; // Import the new entity

/// Parameters for getting paid fees
class GetPaidFeesParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  const GetPaidFeesParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });
}

/// Use case for getting paid fees for a student
class GetPaidFeesUseCase implements UseCase<FinancesResponseEntity, GetPaidFeesParams> {
  final FinancesRepository repository;

  GetPaidFeesUseCase(this.repository);

  @override
  Future<Either<Failure, FinancesResponseEntity>> call(GetPaidFeesParams params) async {
    return await repository.getPaidFees(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}
