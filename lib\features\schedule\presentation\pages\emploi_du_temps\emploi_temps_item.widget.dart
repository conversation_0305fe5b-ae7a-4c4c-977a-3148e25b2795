import 'package:Kairos/features/schedule/domain/entities/emploi_temps_entity.dart';
import 'package:flutter/material.dart';

class EmploiTempsItem extends StatelessWidget {
  final EmploiTempsEntity emploiTemps;

  const EmploiTempsItem({super.key, required this.emploiTemps});

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.symmetric(horizontal: 7, vertical: 1),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Theme.of(context).colorScheme.secondary, width: 1),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.secondary,
              spreadRadius: 0,
              blurRadius: 4,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.only(right: 7),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Color indicator bar
              Container(
                width: 6,
                height: 80, // Adjusted height to accommodate new layout
                decoration: BoxDecoration(
                  color: _getColorFromString(emploiTemps.couleur),
                ),
              ),
              const SizedBox(width: 12),
              // Schedule details column
              Expanded( // Use Expanded to allow the column to take available space
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Row 1: heure and salle
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          emploiTemps.heure,
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                        Text(
                          emploiTemps.salle,
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4), // Spacing between rows
                    // Row 2: cours and type
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          emploiTemps.cours,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                            color: Colors.black,
                          ),
                        ),
                        // Type indicator circle
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: _getColorFromString(emploiTemps.couleur),
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              emploiTemps.type.substring(0, 2),
                              style: const TextStyle(
                                fontSize: 7,
                                fontWeight: FontWeight.w400,
                                color: Colors.white, // Text color is white in Figma
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4), // Spacing between rows
                    // Row 3: professeur and classe | semestre
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Avec ${emploiTemps.professeur}",
                          style: const TextStyle(
                            fontSize: 9,
                            fontWeight: FontWeight.w400,
                            color: Colors.black,
                          ),
                        ),
                        Text(
                          '${emploiTemps.classe} | ${emploiTemps.semestre}',
                          style: const TextStyle(
                            fontSize: 9,
                            fontWeight: FontWeight.w400,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
  }

  Color _getColorFromString(String colorString) {
    // Remove '#' if it exists
    String hexColor = colorString.replaceAll("#", "");
    // If hex color is 6 characters, assume full opacity (FF)
    if (hexColor.length == 6) {
      hexColor = "FF$hexColor";
    }
    try {
      // Parse the hex string into an integer and create a Color object
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      // Handle parsing errors, return a default color
      debugPrint('Error parsing color string $colorString: $e');
      return Colors.blue; // Default color in case of error
    }
  }
}
