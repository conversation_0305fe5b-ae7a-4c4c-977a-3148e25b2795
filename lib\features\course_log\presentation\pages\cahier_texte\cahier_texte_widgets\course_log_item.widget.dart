import 'package:Kairos/features/course_log/data/models/course_log.dart';
import 'package:Kairos/features/course_log/presentation/pages/cahier_texte/cahier_texte_widgets/course_log_details_dialog.widget.dart';
import 'package:flutter/material.dart';
class CourseLogItem extends StatelessWidget {
  final CourseLogEntry logEntry;

  const CourseLogItem({super.key, required this.logEntry});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return CourseLogDetailsDialog(logEntry: logEntry);
          },
        );
      },
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 1.0),
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left side: Day and Date
              Container(
                width: 60, // Adjust width as needed
                margin: const EdgeInsets.only(right: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      logEntry.day,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      logEntry.date,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              // Right side: Course details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${logEntry.timeRange} | ${logEntry.teacher}',
                          style: const TextStyle(fontSize: 14),
                        ),
                        Row(
                          children: [
                            const Icon(Icons.timer, size: 14, color: Colors.grey),
                            const SizedBox(width: 4),
                            Text(
                              logEntry.duration,
                              style: const TextStyle(fontSize: 14, color: Colors.grey),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      logEntry.subject,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Enregistré par ${logEntry.registeredBy} | ${logEntry.registeredDate}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${logEntry.courseCode} | ${logEntry.semester}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
