import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/etablissement_entity.dart';
import '../repositories/schools_repository.dart';

/// Use case for getting the list of all available schools
class GetSchoolsUseCase {
  final SchoolsRepository repository;

  GetSchoolsUseCase(this.repository);

  /// Executes the use case to get the list of schools
  Future<Either<Failure, List<EtablissementEntity>>> call() async {
    return await repository.getAllAvailableSchools();
  }
}