import 'package:equatable/equatable.dart';

/// Entity for child/student information for tutors/parents
class EnfantTuteurEntity extends Equatable {
  final String codeEtudiant;
  final String prenom;
  final String nom;
  final String photo; // base64 string
  final String classeInscrite;

  const EnfantTuteurEntity({
    required this.codeEtudiant,
    required this.prenom,
    required this.nom,
    required this.photo,
    required this.classeInscrite,
  });

  @override
  List<Object?> get props => [
        codeEtudiant,
        prenom,
        nom,
        photo,
        classeInscrite,
      ];
}
