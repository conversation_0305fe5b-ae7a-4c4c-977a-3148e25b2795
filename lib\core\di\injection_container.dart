import 'package:Kairos/features/authentication/data/datasources/activate_school_remote_datasource.dart';
import 'package:Kairos/features/authentication/data/repositories/activate_school_repository_impl.dart';
import 'package:Kairos/features/authentication/domain/repositories/activate_school_repository.dart';
import 'package:Kairos/features/authentication/domain/usecases/verify_pin_usecase.dart';
import 'package:Kairos/features/authentication/presentation/bloc/cubit/code_activation_cubit.dart';
import 'package:Kairos/features/authentication/domain/usecases/activate_school_usecase.dart';
import 'package:get_it/get_it.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Dashboard feature imports
import '../../features/dashboard/data/datasources/dashboard_remote_datasource.dart';
import '../../features/dashboard/data/repositories/dashboard_repository_impl.dart';
import '../../features/dashboard/domain/repositories/dashboard_repository.dart';
import '../../features/dashboard/domain/usecases/load_dashboard_data_usecase.dart';
import '../../features/dashboard/presentation/bloc/dashboard_cubit.dart';

// Student Records feature imports
import '../../features/student_records/data/datasources/student_records_remote_datasource.dart';
import '../../features/student_records/data/repositories/student_records_repository_impl.dart';
import '../../features/student_records/domain/repositories/student_records_repository.dart';
import '../../features/student_records/domain/usecases/get_enfants_du_tuteur_usecase.dart';
import '../../features/student_records/presentation/bloc/student_records_cubit.dart';

// Finances feature imports
import '../../features/finances/data/datasources/finances_remote_datasource.dart';
import '../../features/finances/data/repositories/finances_repository_impl.dart';
import '../../features/finances/domain/repositories/finances_repository.dart';
import '../../features/finances/domain/usecases/get_paid_fees_usecase.dart';
import '../../features/finances/domain/usecases/get_unpaid_fees_usecase.dart';
import '../../features/finances/domain/usecases/get_filtered_paid_fees_usecase.dart';
import '../../features/finances/presentation/bloc/finances_cubit.dart';

// Grades/Notes feature imports
import '../../features/grades/data/datasources/notes_remote_datasource.dart';
import '../../features/grades/data/repositories/notes_repository_impl.dart';
import '../../features/grades/domain/repositories/notes_repository.dart';
import '../../features/grades/domain/usecases/get_notes_evaluations_usecase.dart';
import '../../features/grades/presentation/bloc/notes_cubit.dart';

// Absences feature imports
import '../../features/absences/data/datasources/absences_retards_remote_datasource.dart';
import '../../features/absences/data/repositories/absences_retards_repository_impl.dart';
import '../../features/absences/domain/repositories/absences_retards_repository.dart';
import '../../features/absences/domain/usecases/get_absences_retards_usecase.dart';
import '../../features/absences/domain/usecases/get_absences_retards_filtres_usecase.dart';
import '../../features/absences/presentation/bloc/absences_retards_cubit.dart';

import '../api/api_client.dart';
import '../network/network_info.dart';
import '../services/device_info_service.dart';

// Authentication feature imports
import '../../features/authentication/data/datasources/auth_local_datasource.dart';
import '../../features/authentication/data/datasources/auth_remote_datasource.dart';
import '../../features/authentication/data/repositories/auth_repository_impl.dart';
import '../../features/authentication/domain/repositories/auth_repository.dart';
import '../../features/authentication/domain/usecases/send_sms_usecase.dart';
import '../../features/authentication/domain/usecases/resend_sms_usecase.dart';
import '../../features/authentication/domain/usecases/check_response_usecase.dart';
import '../../features/authentication/presentation/bloc/cubit/auth_cubit.dart';
import '../../features/authentication/presentation/bloc/cubit/phone_authentication_cubit.dart';

// Schools feature imports
import '../../features/schools/data/datasources/schools_remote_datasource.dart';
import '../../features/schools/data/datasources/schools_local_datasource.dart';
import '../../features/schools/data/repositories/schools_repository_impl.dart';
import '../../features/schools/domain/repositories/schools_repository.dart';
import '../../features/schools/domain/usecases/get_schools_usecase.dart';
import '../../features/schools/domain/usecases/get_user_schools_usecase.dart';
import '../../features/schools/domain/usecases/check_financial_status_usecase.dart';
import '../../features/schools/presentation/bloc/schools_cubit.dart';
import '../../features/schools/presentation/bloc/financial_status_cubit.dart';
import '../../features/authentication/presentation/bloc/cubit/activate_school_cubit.dart';
import '../../features/splash/presentation/bloc/splash_cubit.dart';

// Dashboard feature imports
import '../../features/dashboard/data/datasources/dashboard_remote_datasource.dart';
import '../../features/dashboard/data/repositories/dashboard_repository_impl.dart';
import '../../features/dashboard/domain/repositories/dashboard_repository.dart';
import '../../features/dashboard/domain/usecases/load_dashboard_data_usecase.dart';
import '../../features/dashboard/presentation/bloc/dashboard_cubit.dart';

// Profile feature imports
import '../../features/profile/data/datasources/profile_local_datasource.dart';
import '../../features/profile/data/datasources/profile_remote_datasource.dart';
import '../../features/profile/data/repositories/profile_repository_impl.dart';
import '../../features/profile/domain/repositories/profile_repository.dart';
import '../../features/profile/domain/usecases/get_profile_usecase.dart';
import '../../features/profile/domain/usecases/logout_usecase.dart';
import '../../features/profile/presentation/bloc/profile_cubit.dart';

/// Service locator instance
final sl = GetIt.instance;

/// Initialize all dependencies
Future<void> init() async {
  //! Features - Authentication
  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(apiClient: sl()),
  );
  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(sharedPreferences: sl()),
  );
  sl.registerLazySingleton<ActivateSchoolRemoteDatasource>(
    () => ActivateSchoolRemoteDatasourceImpl(apiClient: sl()),
  );

  // Repository
  sl.registerLazySingleton<ActivateSchoolRepository>(
    () => ActivateSchoolRepositoryImpl(remoteDataSource: sl(), authLocalDataSource: sl()),
  );
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => SendSmsUseCase(sl()));
  sl.registerLazySingleton(() => VerifyPinUseCase(sl()));
  sl.registerLazySingleton(() => ResendSmsUseCase(sl()));
  sl.registerLazySingleton(() => CheckResponseUseCase(sl())); // Register the new use case

  // BLoC/Cubit
  sl.registerFactory(() => AuthCubit());
  sl.registerFactory(() => CodeActivationCubit(verifyPinUseCase: sl()));
  sl.registerFactory(() => PhoneAuthenticationCubit(sendSmsUseCase: sl(), 
                                                    resendSmsUseCase: sl(), 
                                                    checkResponseUseCase: sl())); // Provide the new use case
  
  //! Features - Dashboard
  //! Features - Dashboard
  // Data sources
  sl.registerLazySingleton<DashboardRemoteDataSource>(
    () => DashboardRemoteDataSourceImpl(apiClient: sl()),
  );

  // Repository
  sl.registerLazySingleton<DashboardRepository>(
    () => DashboardRepositoryImpl(remoteDataSource: sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => LoadDashboardDataUseCase(sl()));

  // BLoC/Cubit
  sl.registerFactory(() => DashboardCubit(sl()));
  
  //! Features - Finances
  // Data sources
  sl.registerLazySingleton<FinancesRemoteDataSource>(
    () => FinancesRemoteDataSourceImpl(apiClient: sl()),
  );

  // Repository
  sl.registerLazySingleton<FinancesRepository>(
    () => FinancesRepositoryImpl(remoteDataSource: sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => GetPaidFeesUseCase(sl()));
  sl.registerLazySingleton(() => GetUnpaidFeesUseCase(sl()));
  sl.registerLazySingleton(() => GetFilteredPaidFeesUseCase(sl()));

  // BLoC/Cubit
  sl.registerFactory(() => FinancesCubit(
    getPaidFeesUseCase: sl(),
    getUnpaidFeesUseCase: sl(),
    getFilteredPaidFeesUseCase: sl(),
  ));
  
  //! Features - Grades/Notes
  // Data sources
  sl.registerLazySingleton<NotesRemoteDataSource>(
    () => NotesRemoteDataSourceImpl(apiClient: sl()),
  );

  // Repository
  sl.registerLazySingleton<NotesRepository>(
    () => NotesRepositoryImpl(remoteDataSource: sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => GetNotesEvaluationsUseCase(sl()));

  // BLoC/Cubit
  sl.registerFactory(() => NotesCubit(getNotesEvaluationsUseCase: sl()));
  
  //! Features - Schedule
  // TODO: Register schedule dependencies
  
  //! Features - Absences
  // Data sources
  sl.registerLazySingleton<AbsencesRetardsRemoteDataSource>(
    () => AbsencesRetardsRemoteDataSourceImpl(apiClient: sl()),
  );

  // Repository
  sl.registerLazySingleton<AbsencesRetardsRepository>(
    () => AbsencesRetardsRepositoryImpl(remoteDataSource: sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => GetAbsencesRetardsUseCase(sl()));
  sl.registerLazySingleton(() => GetAbsencesRetardsFiltresUseCase(sl()));

  // BLoC/Cubit
  sl.registerFactory(() => AbsencesRetardsCubit(
    getAbsencesRetardsUseCase: sl(),
    getAbsencesRetardsFiltresUseCase: sl(),
  ));
  
  //! Features - Course Log
  // TODO: Register course log dependencies
  
  //! Features - Student Records
  // Data sources
  sl.registerLazySingleton<StudentRecordsRemoteDataSource>(
    () => StudentRecordsRemoteDataSourceImpl(apiClient: sl()),
  );

  // Repository
  sl.registerLazySingleton<StudentRecordsRepository>(
    () => StudentRecordsRepositoryImpl(remoteDataSource: sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => GetEnfantsDuTuteurUseCase(sl()));

  // BLoC/Cubit
  sl.registerFactory(() => StudentRecordsCubit(getEnfantsDuTuteurUseCase: sl()));
  
  //! Features - Profile
  // Data sources
  sl.registerLazySingleton<ProfileLocalDataSource>(
    () => ProfileLocalDataSourceImpl(sharedPreferences: sl()),
  );
  sl.registerLazySingleton<ProfileRemoteDataSource>(
    () => ProfileRemoteDataSourceImpl(apiClient: sl()),
  );

  // Repository
  sl.registerLazySingleton<ProfileRepository>(
    () => ProfileRepositoryImpl(
      localDataSource: sl(),
      remoteDataSource: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetProfileUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));

  // BLoC/Cubit
  sl.registerFactory(() => ProfileCubit(
    getProfileUseCase: sl(),
    logoutUseCase: sl(),
  ));
  
  //! Features - Notifications
  // TODO: Register notifications dependencies
  
  //! Features - Schools
  // Data sources
  sl.registerLazySingleton<SchoolsRemoteDataSource>(
    () => SchoolsRemoteDataSourceImpl(apiClient: sl()),
  );
  sl.registerLazySingleton<SchoolsLocalDataSource>(
    () => SchoolsLocalDataSourceImpl(sharedPreferences: sl()),
  );

  // Repository
  sl.registerLazySingleton<SchoolsRepository>(
    () => SchoolsRepositoryImpl(remoteDataSource: sl(), localDataSource: sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => GetSchoolsUseCase(sl()));
  sl.registerLazySingleton(() => GetUserSchoolsUseCase(sl())); // Register GetUserSchoolsUseCase
  sl.registerLazySingleton(() => CheckFinancialStatusUseCase(sl())); // Register CheckFinancialStatusUseCase
  sl.registerLazySingleton(() => ActivateSchoolUseCase(sl())); // Register ActivateSchoolUseCase

  sl.registerFactory(() => SchoolsCubit(sl(), sl())); // Provide use cases for schools management
  sl.registerFactory(() => FinancialStatusCubit(sl())); // Register FinancialStatusCubit with CheckFinancialStatusUseCase
  sl.registerFactory(() => ActivateSchoolCubit(sl(), sl())); // Register ActivateSchoolCubit with new dependencies

  //! Features - Splash
  // BLoC/Cubit
  sl.registerFactory(() => SplashCubit(sl())); // Register SplashCubit with AuthLocalDataSource
  
  //! Core
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(sl()));
  sl.registerLazySingleton(() => DeviceInfoService());
  sl.registerLazySingleton<ApiClient>(() => ApiClient(authLocalDataSource: sl()));
  
  //! External
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);
  sl.registerLazySingleton(() => Connectivity());
}

/// Register feature-specific dependencies
/// This method will be called for each feature as they are implemented
void registerFeatureDependencies() {
  // TODO: Implement feature-specific dependency registration
}
