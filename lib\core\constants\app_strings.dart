/// Application string constants for localization
class AppStrings {
  // Common
  static const String loading = 'Chargement...';
  static const String error = 'Erreur';
  static const String success = 'Succès';
  static const String cancel = 'Annuler';
  static const String confirm = 'Confirmer';
  static const String save = 'Enregistrer';
  static const String delete = 'Supprimer';
  static const String edit = 'Modifier';
  static const String search = 'Rechercher';
  static const String filter = 'Filtrer';
  static const String refresh = 'Actualiser';
  static const String retry = 'Réessayer';
  static const String close = 'Fermer';
  static const String back = 'Retour';
  static const String next = 'Suivant';
  static const String previous = 'Précédent';
  static const String finish = 'Terminer';
  
  // Authentication
  static const String login = 'Connexion';
  static const String logout = 'Déconnexion';
  static const String register = 'Inscription';
  static const String email = 'Email';
  static const String password = 'Mot de passe';
  static const String confirmPassword = 'Confirmer le mot de passe';
  static const String forgotPassword = 'Mot de passe oublié ?';
  static const String rememberMe = 'Se souvenir de moi';
  static const String loginSuccess = 'Connexion réussie';
  static const String loginError = 'Erreur de connexion';
  static const String invalidCredentials = 'Identifiants invalides';
  
  // Dashboard
  static const String dashboard = 'Tableau de bord';
  static const String welcome = 'Bienvenue';
  static const String quickActions = 'Actions rapides';
  
  // Features
  static const String finances = 'Finances';
  static const String grades = 'Notes';
  static const String schedule = 'Emploi du temps';
  static const String absences = 'Absences';
  static const String courseLog = 'Cahier de texte';
  static const String studentRecords = 'Dossiers';
  static const String profile = 'Profil';
  static const String notifications = 'Notifications';
  static const String schools = 'Établissements';
  
  // Empty states
  static const String noDataFound = 'Aucune donnée trouvée';
  static const String noResultsFound = 'Aucun résultat trouvé';
  static const String emptyList = 'Liste vide';
  
  // Network
  static const String noInternetConnection = 'Pas de connexion internet';
  static const String connectionTimeout = 'Délai de connexion dépassé';
  static const String serverError = 'Erreur du serveur';
}
