import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/auth_repository.dart';

/// Use case for verifying activation PIN
class VerifyPinUseCase {
  final AuthRepository repository;

  VerifyPinUseCase(this.repository);

  /// Execute the verify PIN use case
  ///
  /// [fullName] - The user's full name
  /// [otp] - The entered OTP/PIN
  /// Returns [Either<Failure, dynamic>] - Success or failure result
  Future<Either<Failure, dynamic>> call(String fullName, String otp, String phoneNumber) async {
    return await repository.verifyPinWithDetails(fullName, otp, phoneNumber); // Pass empty device info map
  }
}