﻿import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/get_enfants_du_tuteur_usecase.dart';
import 'student_records_state.dart';

/// StudentRecords Cubit for managing student_records state
class StudentRecordsCubit extends Cubit<StudentRecordsState> {
  final GetEnfantsDuTuteurUseCase getEnfantsDuTuteurUseCase;

  StudentRecordsCubit({required this.getEnfantsDuTuteurUseCase}) : super(const StudentRecordsInitial());

  /// Load children data for tutor/parent
  Future<void> loadEnfantsDuTuteur({
    required String codeUtilisateur,
    required String codeEtab,
    required String telephone,
  }) async {
    emit(const StudentRecordsLoading());

    try {
      final params = GetEnfantsDuTuteurParams(
        codeUtilisateur: codeUtilisateur,
        codeEtab: codeEtab,
        telephone: telephone,
      );

      final result = await getEnfantsDuTuteurUseCase(params);

      result.fold(
        (failure) => emit(StudentRecordsError(failure.message)),
        (children) => emit(StudentRecordsLoaded(children)),
      );
    } catch (e) {
      emit(StudentRecordsError('Erreur inattendue: $e'));
    }
  }

  /// Refresh children data
  Future<void> refresh({
    required String codeUtilisateur,
    required String codeEtab,
    required String telephone,
  }) async {
    await loadEnfantsDuTuteur(
      codeUtilisateur: codeUtilisateur,
      codeEtab: codeEtab,
      telephone: telephone,
    );
  }
}
