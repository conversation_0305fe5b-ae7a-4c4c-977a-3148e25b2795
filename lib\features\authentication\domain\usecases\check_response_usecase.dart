import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/auth_repository.dart';

/// Use case for checking the user's response to the reactivation dialog.
class CheckResponseUseCase {
  final AuthRepository repository;

  CheckResponseUseCase(this.repository);

  /// Calls the repository to check the user's response.
  ///
  /// [phoneNumber] - The complete phone number including country code.
  /// [activated] - Whether the user chose to reactivate (true) or not (false).
  Future<Either<Failure, dynamic>> call(String phoneNumber, {required bool activated}) async {
    return await repository.checkResponse(phoneNumber, activated: activated);
  }
}