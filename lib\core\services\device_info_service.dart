import "dart:convert";
import "dart:io";
import "package:Kairos/core/device_info.dart";
import "package:device_info_plus/device_info_plus.dart";
import "package:flutter/material.dart";
import "package:mobile_device_identifier/mobile_device_identifier.dart";
import "package:shared_preferences/shared_preferences.dart";


class DeviceInfoService {

  static final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();
  static final MobileDeviceIdentifier _mobileDeviceIdentifer = MobileDeviceIdentifier();
  static late final DeviceInfo _deviceInfo;
  static DeviceInfo get deviceInfo => _deviceInfo;



    static Future<void> init() async {
      debugPrint("DeviceInfoService: INITIALIZING DEVICE INFO");
      
      try{
        if(await _isDeviceInfoAlreadyStored()){
          _deviceInfo = await loadDeviceInfo();
        } else {
        if(Platform.isAndroid){
          _deviceInfo = await _readAndroidDeviceInfo(await _deviceInfoPlugin.androidInfo);
          saveDeviceInfo(_deviceInfo);
        } else if(Platform.isIOS){
          _deviceInfo = await _readIosDeviceInfo(await _deviceInfoPlugin.iosInfo);
          saveDeviceInfo(_deviceInfo);
        } else {
          throw Exception("Platform not supported");
        }
      }
      }
      catch(e){
        debugPrint("DeviceInfoService: ERROR: $e");
        _deviceInfo = DeviceInfo(
          marqueTelephone: "Unknown",
          modelTelephone: "Unknown",
          imeiTelephone: "Unknown",
          numeroSerie: "Unknown",
          );
      }
    }


static void saveDeviceInfo(DeviceInfo deviceInfo) async{
  final prefs = await SharedPreferences.getInstance();
  debugPrint("DeviceInfoService: SAVING DEVICE INFO: ${jsonEncode(deviceInfo.toJson())}");
  prefs.setString("deviceInfo", jsonEncode(deviceInfo.toJson()));
}



static Future<bool> _isDeviceInfoAlreadyStored() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.containsKey("deviceInfo");
}

static Future<DeviceInfo> loadDeviceInfo() async{
  final prefs = await SharedPreferences.getInstance();
  final deviceInfoStringifiedJson = prefs.getString("deviceInfo");

  // Add try-catch to handle potential FormatException from invalid stored data
  try {
    final deviceInfoJson = jsonDecode(deviceInfoStringifiedJson!);
    return DeviceInfo.fromJson(deviceInfoJson!);
  } on FormatException catch (e) {
    // If decoding fails, it means the stored data is not valid JSON.
    // Clear the invalid data and re-initialize device info.
    debugPrint("DeviceInfoService: FormatException during load: $e. Clearing stored device info and re-initializing.");
    await prefs.remove("deviceInfo");
    // Call init to regenerate and save device info correctly
    await init();
    // After re-initialization, the correct deviceInfo should be available
    return _deviceInfo;
  } catch (e) {
    // Handle other potential errors during loading
    debugPrint("DeviceInfoService: Error loading device info: $e");
    // Re-throw or return a default DeviceInfo based on desired error handling
    // For now, re-throw to indicate a problem beyond FormatException
    rethrow;
  }
}






  static Future <DeviceInfo> _readAndroidDeviceInfo(AndroidDeviceInfo androidBuild) async{
    debugPrint("DeviceInfoService: DEVICE INFO: ${androidBuild.toString()}");
    String? uniqueDeviceId = await _getUniqueDeviceId();
    return DeviceInfo(
      marqueTelephone: androidBuild.brand, 
      modelTelephone: androidBuild.model, 
      imeiTelephone: uniqueDeviceId!, 
      numeroSerie: androidBuild.id
      );

  }



  static Future<DeviceInfo> _readIosDeviceInfo(IosDeviceInfo iosBuild) async{
    debugPrint("DeviceInfoService: DEVICE INFO: $iosBuild");
    String? uniqueDeviceId = await _getUniqueDeviceId();

    return DeviceInfo(
      marqueTelephone: iosBuild.name, 
      modelTelephone: iosBuild.model, 
      imeiTelephone: uniqueDeviceId!, 
      numeroSerie: iosBuild.identifierForVendor!
      );

  }


  static Future<String?> _getUniqueDeviceId() async{
    return await _mobileDeviceIdentifer.getDeviceId();
  }



}

