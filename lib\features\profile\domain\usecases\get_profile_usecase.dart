import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/profile_entity.dart';
import '../repositories/profile_repository.dart';

/// Use case for getting user profile from local storage using codeUtilisateur
class GetProfileUseCase implements UseCase<ProfileEntity?, String> { // Update UseCase parameters
  final ProfileRepository repository;

  GetProfileUseCase(this.repository);

  @override
  Future<Either<Failure, ProfileEntity?>> call(String codeUtilisateur) async { // Accept codeUtilisateur
    // Pass the codeUtilisateur to the repository
    return await repository.getProfile(codeUtilisateur);
  }
}
