import '../../../../core/api/api_client.dart';
import '../models/dashboard_model.dart';

/// Abstract remote data source for dashboard operations
abstract class DashboardRemoteDataSource {
  /// Load dashboard data from remote API
  Future<DashboardModel> loadDashboardData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}

/// Implementation of DashboardRemoteDataSource
class DashboardRemoteDataSourceImpl implements DashboardRemoteDataSource {
  final ApiClient apiClient;

  DashboardRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<DashboardModel> loadDashboardData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Build query parameters based on whether codeUtilisateur is provided
      final queryParameters = <String, dynamic>{
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur if provided (for parent/tutor workflow)
      if (codeUtilisateur != null) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      // Make API call with query parameters
      final response = await apiClient.getWithToken(
        '/dashboard',
        queryParameters: queryParameters,
      );

      // Parse response and return model
      return DashboardModel.fromJson(response.data);
    } catch (e) {
      // Handle errors appropriately - convert to appropriate exceptions
      throw Exception('Failed to load dashboard data: $e');
    }
  }
}
