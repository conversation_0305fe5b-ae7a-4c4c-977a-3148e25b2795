import '../../domain/entities/etablissement_utilisateur.dart'; // Import the entity

/// Data model for user-associated school information
class EtablissementUtilisateurModel {
  final EtablissementModel etablissement;
  final String profil;
  final String codeUtilisateur;

  EtablissementUtilisateurModel({
    required this.etablissement,
    required this.profil,
    required this.codeUtilisateur,
  });

  factory EtablissementUtilisateurModel.fromJson(Map<String, dynamic> json) {
    return EtablissementUtilisateurModel(
      etablissement: EtablissementModel.fromJson(json['etablissement']),
      profil: json['profil'],
      codeUtilisateur: json['codeUtilisateur'],
    );
  }

  /// Converts EtablissementUtilisateurModel to EtablissementUtilisateur entity
  EtablissementUtilisateur toEntity() {
    return EtablissementUtilisateur(
      libelleEtab: etablissement.libelleEtab,
      codeEtab: etablissement.codeEtab,
      logoEtablissement: etablissement.logoEtablissement,
      profil: profil,
      codeUtilisateur: codeUtilisateur,
    );
  }
}

/// Nested model for etablissement details within EtablissementUtilisateurModel
class EtablissementModel {
  final String libelleEtab;
  final String codeEtab;
  final String logoEtablissement;

  EtablissementModel({
    required this.libelleEtab,
    required this.codeEtab,
    required this.logoEtablissement,
  });

  factory EtablissementModel.fromJson(Map<String, dynamic> json) {
    return EtablissementModel(
      libelleEtab: json['libelleEtab'],
      codeEtab: json['codeEtab'],
      logoEtablissement: json['logoEtablissement'],
    );
  }
}