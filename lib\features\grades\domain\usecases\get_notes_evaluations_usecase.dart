import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/notes_evaluation_entity.dart';
import '../repositories/notes_repository.dart';

/// Parameters for GetNotesEvaluationsUseCase
class GetNotesEvaluationsParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  const GetNotesEvaluationsParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });
}

/// Use case for getting notes evaluations
class GetNotesEvaluationsUseCase implements UseCase<List<NotesEvaluationEntity>, GetNotesEvaluationsParams> {
  final NotesRepository repository;

  const GetNotesEvaluationsUseCase(this.repository);

  @override
  Future<Either<Failure, List<NotesEvaluationEntity>>> call(GetNotesEvaluationsParams params) async {
    return await repository.getNotesEvaluations(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}
