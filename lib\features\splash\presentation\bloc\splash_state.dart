﻿import 'package:equatable/equatable.dart';

/// Base splash state
abstract class SplashState extends Equatable {
  const SplashState();
  
  @override
  List<Object?> get props => [];
}

/// Initial splash state
class SplashInitial extends SplashState {
  const SplashInitial();
}

/// Loading state during splash operations
class SplashLoading extends SplashState {
  const SplashLoading();
}

/// Splash data loaded successfully
class SplashLoaded extends SplashState {
  final List<dynamic> data; // TODO: Replace with proper type
  
  const SplashLoaded({required this.data});
  
  @override
  List<Object?> get props => [data];
}

/// Splash error occurred
class SplashError extends SplashState {
  final String message;

  const SplashError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Authentication check completed - user is authenticated
class SplashAuthenticated extends SplashState {
  const SplashAuthenticated();
}

/// Authentication check completed - user is not authenticated
class SplashUnauthenticated extends SplashState {
  const SplashUnauthenticated();
}
