import 'package:dio/dio.dart';
import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../../../../core/api/api_exception.dart';
import '../../../../core/error/exceptions.dart';
import '../../../authentication/data/models/deconnexion_request.dart';

/// Abstract interface for profile remote data source
abstract class ProfileRemoteDataSource {
  /// Call suppressionEtablissement API for logout
  Future<void> logout(DeconnexionRequest request);
}

/// Implementation of ProfileRemoteDataSource
class ProfileRemoteDataSourceImpl implements ProfileRemoteDataSource {
  final ApiClient apiClient;

  ProfileRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<void> logout(DeconnexionRequest request) async {
    try {
      // Make HTTP POST request to the suppressionEtablissement endpoint
      final response = await apiClient.postWithToken(
        ApiEndpoints.suppressionEtablissement,
        data: request.toJson(),
      );

      // Check if the response is successful
      if (response.statusCode != 200) {
        throw ServerException('Failed to logout with status code: ${response.statusCode}');
      }

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion lors de la déconnexion: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la déconnexion: $e');
    }
  }
}
