import 'dart:convert';
import 'package:Kairos/features/profile/data/profile_type.enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/widgets/common/snackbar_widget.dart';
import 'package:Kairos/core/services/device_info_service.dart';
import 'package:Kairos/features/authentication/data/models/deconnexion_request.dart';
import '../../../domain/entities/profile_entity.dart';
import '../../bloc/profile_cubit.dart';
import '../../bloc/profile_state.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/core/theme/color_schemes.dart';
import 'package:Kairos/core/widgets/dialogs/app_alert_dialog.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key, this.etablissementUser});

  final EtablissementUtilisateur? etablissementUser;

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  @override
  void initState() {
    super.initState();
    // Load profile data when page initializes, passing the codeUtilisateur
    if (widget.etablissementUser != null) {
      context.read<ProfileCubit>().loadProfileData(widget.etablissementUser!.codeUtilisateur);
    }
  }

  void _goToListSchools(){
    debugPrint("User wants to his list of schools");
    Navigator.popUntil(context, ModalRoute.withName('/liste_etablissement'));
  }


  Future<void> _logout(ProfileEntity profile) async {
    try {
      // Get device info
      final deviceInfo = DeviceInfoService.deviceInfo;

      // Create deconnexion request
      final request = DeconnexionRequest(
        numeroTelephone: profile.phoneNumber,
        marqueTelephone: deviceInfo.marqueTelephone,
        modelTelephone: deviceInfo.modelTelephone,
        imeiTelephone: deviceInfo.imeiTelephone,
        numeroSerie: deviceInfo.numeroSerie,
        codeUtilisateur: profile.codeUtilisateur,
        codeEtab: profile.schoolCode,
      );

    debugPrint('ProfilePage: Logout request: ${request.toJson()}');
      // Call logout
      context.read<ProfileCubit>().logout(request);
    } catch (e) {
      debugPrint('Error creating logout request: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    const double heroHeight = 200.0;

    return BlocListener<ProfileCubit, ProfileState>(
      listener: (context, state) {
        if (state is LogoutSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: "Votre demande de suppression de votre espace a été enregistrée avec succès",
            ).getSnackBar(),
          );
          Navigator.pushReplacementNamed(context, '/liste_etablissement');
        } else if (state is ProfileError) {
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(message: state.message).getSnackBar(),
          );
        }
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          title: const Text('Profil'),
          centerTitle: false,
          foregroundColor: Colors.white,
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: heroHeight + 150, // Extended height to accommodate floating card
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // Hero background image
                    Hero(
                      tag: "hero_profile",
                      transitionOnUserGestures: true,
                      child: Image.asset("assets/images/header_dashboard.png", width: MediaQuery.of(context).size.width, fit: BoxFit.cover,),
                    ),
                    // Floating profile card positioned over hero section
                    Positioned(
                      top: 83, // 83px from screen top
                      left: (MediaQuery.of(context).size.width - 302) / 2, // Centered horizontally
                      child: BlocBuilder<ProfileCubit, ProfileState>(
                        builder: (context, state) {
                          if (state is ProfileLoaded) {
                            return _buildFloatingProfileCard(state.profile);
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 0, left: 16, right: 16),
                child: BlocBuilder<ProfileCubit, ProfileState>(
                  builder: (context, state) {
                    if (state is ProfileLoading || state is LogoutLoading) {
                      return Center(
                        heightFactor: 4,
                        child: CustomSpinner(
                          size: 60.0,
                          strokeWidth: 5.0,
                        ),
                      );
                    } else if (state is ProfileNotFound) {
                      return Center(
                        heightFactor: 2,
                        child: Text(
                          "Vous n'avez pas encore activé votre espace, veuillez activer un établissement afin d'accéder à votre profil",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      );
                    } else if (state is ProfileLoaded) {
                      return _buildProfileContent(state.profile);
                    } else if (state is ProfileError) {
                      return Center(
                        heightFactor: 4,
                        child: Column(
                          children: [
                            Text(
                              'Erreur: ${state.message}',
                              textAlign: TextAlign.center,
                              style: const TextStyle(color: Colors.red),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () => context.read<ProfileCubit>().loadProfileData(widget.etablissementUser!.codeUtilisateur),
                              child: const Text('Réessayer'),
                            ),
                          ],
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileContent(ProfileEntity profile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // School section
        _buildInfoSection('Etablissement', "${profile.schoolCode} - ${profile.schoolName}", hasIcon: true),
        const SizedBox(height: 7),

        // Phone section
        _buildInfoSection('Téléphone', profile.phoneNumber),
        const SizedBox(height: 7),

        // Voir les espaces section
        _buildActionSection(),
        const SizedBox(height: 12),

        // Logout button
        Center(
          child: Container(
            width: 250,
            height: 46,
            decoration: BoxDecoration(
              color: const Color(0xFF920000),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Center(
              child: GestureDetector(
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AppAlertDialog(
                        title: "DECONNEXION ETABLISSEMENT",
                        content: const Text(
                          "Êtes-vous sûr de vouloir vous déconnecter de votre établissement? ",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                          ),
                        ),
                        primaryActionCallback: () => _logout(profile),
                      );
                    },
                  );
                },
                child: Text(
                  'DÉCONNEXION ÉTABLISSEMENT',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    height: 1.21,
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 20),

        // Delete account section
        _buildDeleteAccountSection(profile),
        const SizedBox(height: 40),
      ],
    );
  }

  Widget _buildFloatingProfileCard(ProfileEntity profile) {
    return Stack(
      children: [
        // Main profile card matching Figma Rectangle 58
        Container(
          width: 302,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.25),
                offset: const Offset(0, 4),
                blurRadius: 4,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            children: [
              const SizedBox(height: 106), // Space for profile image
              // Update photo text
              const Text(
                'Mettre à jour la photo',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF055A28),
                  height: 1.21,
                ),
              ),
              const SizedBox(height: 13),
              // User name
              Text(
                profile.fullName,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w800,
                  color: Colors.black,
                  height: 1.21,
                ),
              ),
              const SizedBox(height: 13),
              // Profile type with bold styling
              RichText(
                text: TextSpan(
                  children: [
                    const TextSpan(
                      text: 'Profil ',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w800,
                        color: Colors.black,
                        height: 1.21,
                      ),
                    ),
                    TextSpan(
                      text: profile.profil == ProfileType.etudiant.code ? 'Étudiant' : profile.profil == ProfileType.tuteur.code ? 'Tuteur' : 'Parent',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w800,
                        color: Colors.black,
                        height: 1.21,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 71),
            ],
          ),
        ),
        // Profile image positioned on top of the card
        Positioned(
          left: 116.5, // Center of 302px card (151px) - half of 69px image (34.5px)
          top: 24, // Position from top of card
          child: _buildProfileImage(profile),
        ),
      ],
    );
  }

  Widget _buildInfoSection(String title, String value, {bool hasIcon = false}) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.black.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black.withValues(alpha: 0.6),
                height: 1.21,
              ),
            ),
            const SizedBox(height: 2),
            // Value with optional icon
            Row(
              children: [
                if (hasIcon) ...[
                  Container(
                    width: 23,
                    height: 23,
                    decoration:
                        BoxDecoration(
                      image: DecorationImage(
                        image: MemoryImage(
                                        base64Decode(widget.etablissementUser!.logoEtablissement.replaceFirst(RegExp(r'^data:image\/[^;]+;base64,'), '')),
                                      ), // You'll need to add this asset
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: Text(
                    value,
                    style: TextStyle(
      
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.black.withValues(alpha: 0.6),
                      height: 1.21,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.black.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _goToListSchools,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 8),
            child: Row(
              children: [
                // Arrow icons
                Image.asset('assets/icons/arrow_right.png'), // You'll need to add this asset
                Image.asset('assets/icons/arrow_right.png'), // You'll need to add this asset
                
                const SizedBox(width: 16),
                // Text
                Text(
                  'Voir les espaces',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                    height: 1.21,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDeleteAccountSection(ProfileEntity profile) {
    return Center(
      child: GestureDetector(
        onTap: () {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AppAlertDialog(
                title: "SUPPRESSION DE COMPTE",
                content: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    children: [
                      const TextSpan(
                        text: "Vous serez déconnecté de votre profil !\n",
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                        ),
                      ),
                      TextSpan(
                        text: "En continuant, les informations renseignées seront supprimées de cet appareil.\nVoulez-vous continuer ?",
                        style: TextStyle(
                          color: AppColorSchemes.errorRed,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                primaryActionText: 'SUPPRIMER',
                primaryActionCallback: () => _logout(profile),
              );
            },
          );
        },
        child: const Text(
          'Supprimer mon compte',
          style: TextStyle(
           
            fontSize: 13,
            fontWeight: FontWeight.w300,
            color: Color(0xFF920000),
            height: 1.21,
          ),
        ),
      ),
    );
  }

  Widget _buildProfileImage(ProfileEntity profile) {
    ImageProvider backgroundImage;
    if (profile.photo.isNotEmpty) {
      debugPrint("ProfilePage: Building profile image with photo: ${profile.photo}");
      try {
        backgroundImage = MemoryImage(
          base64Decode(profile.photo.replaceFirst(RegExp(r'^data:image\/[^;]+;base64,'), '')),
        );
      } catch (_) {
        backgroundImage = const AssetImage('assets/images/default_profile_image.jpg');
      }
    } else {
      debugPrint("ProfilePage: Building profile image with default image");
      backgroundImage = const AssetImage('assets/images/default_profile_image.jpg');
    }

    return Container(
      width: 69,
      height: 69,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: CircleAvatar(
        radius: 32.5,
        backgroundImage: backgroundImage,
      ),
    );
  }
}
