﻿import 'package:equatable/equatable.dart';
import '../../domain/entities/etablissement_utilisateur.dart'; // Import EtablissementUtilisateur
import '../../domain/entities/etablissement_entity.dart'; // Import EtablissementEntity

/// Base schools state
abstract class SchoolsState extends Equatable {
  const SchoolsState();
  
  @override
  List<Object?> get props => [];
}

/// Initial schools state
class SchoolsInitial extends SchoolsState {
  const SchoolsInitial();
}

/// Loading state during schools operations
class SchoolsLoading extends SchoolsState {
  const SchoolsLoading();
}

/// User schools data loaded successfully
class SchoolsLoaded extends SchoolsState {
  final List<EtablissementUtilisateur> data; // Use correct type
  
  const SchoolsLoaded({required this.data});
  
  @override
  List<Object?> get props => [data];
}

/// General schools data loaded successfully
class GeneralSchoolsLoaded extends SchoolsState {
  final List<EtablissementEntity> data; // State for general schools
  
  const GeneralSchoolsLoaded({required this.data});
  
  @override
  List<Object?> get props => [data];
}

/// Schools list is empty
class SchoolsEmpty extends SchoolsState {
  const SchoolsEmpty();
}

/// Schools error occurred
class SchoolsError extends SchoolsState {
  final String message;

  const SchoolsError(this.message);

  @override
  List<Object?> get props => [message];
}
