import 'package:equatable/equatable.dart';

/// Entity for dashboard data
class DashboardEntity extends Equatable {
  final String anneeScolaire;
  final NoteEntity note;
  final AssiduiteEntity assiduite;
  final EtatFinancierEntity etatFinancier;
  final DossierEntity dossier;
  final RessourcesPedagogiquesEntity ressourcesPedagogiques;
  final PlanningsEntity plannings;
  final CahierTexteEntity cahierTexte;

  const DashboardEntity({
    required this.anneeScolaire,
    required this.note,
    required this.assiduite,
    required this.etatFinancier,
    required this.dossier,
    required this.ressourcesPedagogiques,
    required this.plannings,
    required this.cahierTexte,
  });

  @override
  List<Object?> get props => [
        anneeScolaire,
        note,
        assiduite,
        etatFinancier,
        dossier,
        ressourcesPedagogiques,
        plannings,
        cahierTexte,
      ];
}

/// Entity for note information
class NoteEntity extends Equatable {
  final int nombreNote;

  const NoteEntity({
    required this.nombreNote,
  });

  @override
  List<Object?> get props => [nombreNote];
}

/// Entity for assiduite (attendance) information
class AssiduiteEntity extends Equatable {
  final int nombreAbsence;
  final int nombreRetard;

  const AssiduiteEntity({
    required this.nombreAbsence,
    required this.nombreRetard,
  });

  @override
  List<Object?> get props => [nombreAbsence, nombreRetard];
}

/// Entity for financial status information
class EtatFinancierEntity extends Equatable {
  final double montantEncaisse;
  final double montantAencaisser;
  final double montantImpaye;

  const EtatFinancierEntity({
    required this.montantEncaisse,
    required this.montantAencaisser,
    required this.montantImpaye,
  });

  @override
  List<Object?> get props => [montantEncaisse, montantAencaisser, montantImpaye];
}

/// Entity for dossier (student records) information
class DossierEntity extends Equatable {
  final int nombreElement;

  const DossierEntity({
    required this.nombreElement,
  });

  @override
  List<Object?> get props => [nombreElement];
}

/// Entity for educational resources information
class RessourcesPedagogiquesEntity extends Equatable {
  final int nombreRessource;

  const RessourcesPedagogiquesEntity({
    required this.nombreRessource,
  });

  @override
  List<Object?> get props => [nombreRessource];
}

/// Entity for planning information
class PlanningsEntity extends Equatable {
  final int nombrePlanningSemaine;
  final int nombrePlanningJour;

  const PlanningsEntity({
    required this.nombrePlanningSemaine,
    required this.nombrePlanningJour,
  });

  @override
  List<Object?> get props => [nombrePlanningSemaine, nombrePlanningJour];
}

/// Entity for cahier texte (course log) information
class CahierTexteEntity extends Equatable {
  final int nombreEntre;
  final String semestre;

  const CahierTexteEntity({
    required this.nombreEntre,
    required this.semestre,
  });

  @override
  List<Object?> get props => [nombreEntre, semestre];
}
