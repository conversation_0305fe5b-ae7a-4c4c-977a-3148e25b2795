import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/enfant_tuteur_entity.dart';
import '../repositories/student_records_repository.dart';

/// Parameters for GetEnfantsDuTuteurUseCase
class GetEnfantsDuTuteurParams {
  final String codeUtilisateur;
  final String codeEtab;
  final String telephone;

  GetEnfantsDuTuteurParams({
    required this.codeUtilisateur,
    required this.codeEtab,
    required this.telephone,
  });
}

/// Use case for getting children/students for a tutor/parent
class GetEnfantsDuTuteurUseCase implements UseCase<List<EnfantTuteurEntity>, GetEnfantsDuTuteurParams> {
  final StudentRecordsRepository repository;

  GetEnfantsDuTuteurUseCase(this.repository);

  @override
  Future<Either<Failure, List<EnfantTuteurEntity>>> call(GetEnfantsDuTuteurParams params) async {
    return await repository.getEnfantsDuTuteur(
      codeUtilisateur: params.codeUtilisateur,
      codeEtab: params.codeEtab,
      telephone: params.telephone,
    );
  }
}
