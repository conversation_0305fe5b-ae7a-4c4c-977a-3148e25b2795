import 'package:Kairos/features/finances/data/models/finances_response_model.dart'; // Corrected import path
import 'package:Kairos/features/finances/domain/entities/frais_etudiant_entity.dart'; // Corrected import path
import 'package:equatable/equatable.dart';

/// Represents the financial summary and paid fees for a student in the domain layer.
class FinancesResponseEntity extends Equatable {
  /// The total amount collected.
  final double? montantEncaisser;

  /// The total amount yet to be collected.
  final double? montantAEncaisser;

  /// The reference year for the financial data.
  final String? anneeReference;

  /// A list of paid student fees.
  final List<FraisEtudiantEntity>? fraisPayes;

  /// Constructs a [FinancesResponseEntity].
  const FinancesResponseEntity({
    required this.montantEncaisser,
    required this.montantAEncaisser,
    required this.anneeReference,
    required this.fraisPayes,
  });

  /// Factory constructor to create a [FinancesResponseEntity] from a [FinancesResponseModel].
  factory FinancesResponseEntity.fromModel(FinancesResponseModel model) {
    return FinancesResponseEntity(
      montantEncaisser: model.montantEncaisser,
      montantAEncaisser: model.montantAEncaisser,
      anneeReference: model.anneeReference,
      // Convert list of FraisEtudiantModel to FraisEtudiantEntity
      fraisPayes: model.fraisPayes
          ?.map<FraisEtudiantEntity>((model) => FraisEtudiantEntity.fromModel(model))
          .toList(),
    );
  }

  @override
  List<Object?> get props => [
        montantEncaisser,
        montantAEncaisser,
        anneeReference,
        fraisPayes,
      ];
}