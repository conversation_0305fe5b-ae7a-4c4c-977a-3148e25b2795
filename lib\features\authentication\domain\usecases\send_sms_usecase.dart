import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/auth_repository.dart';
import '../entities/sms_response_entity.dart'; // Import the new entity

/// Use case for sending SMS verification code
class SendSmsUseCase {
  final AuthRepository repository;
  
  SendSmsUseCase(this.repository);
  
  /// Execute the send SMS use case
  /// 
  /// [phoneNumber] - The phone number to send SMS to
  /// Returns [Either<Failure, void>] - Success or failure result
  Future<Either<Failure, SmsResponseEntity>> call(String phoneNumber) async { // Update return type
    return await repository.sendSms(phoneNumber);
  }
}
