import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/finances_unpaid_response_entity.dart';
import '../repositories/finances_repository.dart';

/// Parameters for getting unpaid fees
class GetUnpaidFeesParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  const GetUnpaidFeesParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });
}

/// Use case for getting unpaid fees for a student
class GetUnpaidFeesUseCase implements UseCase<FinancesUnpaidResponseEntity, GetUnpaidFeesParams> {
  final FinancesRepository repository;

  GetUnpaidFeesUseCase(this.repository);

  @override
  Future<Either<Failure, FinancesUnpaidResponseEntity>> call(GetUnpaidFeesParams params) async {
    return await repository.getUnpaidFees(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}
