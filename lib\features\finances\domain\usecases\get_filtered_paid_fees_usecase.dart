import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/finances_repository.dart';
import '../entities/finances_response_entity.dart'; // Import the new entity

/// Parameters for GetFilteredPaidFeesUseCase
class GetFilteredPaidFeesParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String dateDebut;
  final String dateFin;
  final String? codeUtilisateur;

  GetFilteredPaidFeesParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    required this.dateDebut,
    required this.dateFin,
    this.codeUtilisateur,
  });
}

/// Use case for getting filtered paid fees with date range
class GetFilteredPaidFeesUseCase implements UseCase<FinancesResponseEntity, GetFilteredPaidFeesParams> {
  final FinancesRepository repository;

  GetFilteredPaidFeesUseCase(this.repository);

  @override
  Future<Either<Failure, FinancesResponseEntity>> call(GetFilteredPaidFeesParams params) async {
    return await repository.getFilteredPaidFees(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      dateDebut: params.dateDebut,
      dateFin: params.dateFin,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}
