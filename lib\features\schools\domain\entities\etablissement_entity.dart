import 'package:equatable/equatable.dart';

/// Entity for Etablissement (School)
class EtablissementEntity extends Equatable {
  final String? id;
  final String libelleEtab;
  final String codeEtab;
  final String logoEtablissement;
  final String type;
  final String localisation;

  const EtablissementEntity({
    this.id,
    required this.libelleEtab,
    required this.codeEtab,
    required this.logoEtablissement,
    required this.type,
    required this.localisation,
  });

  @override
  List<Object?> get props => [
        id,
        libelleEtab,
        codeEtab,
        logoEtablissement,
        type,
        localisation,
      ];
}