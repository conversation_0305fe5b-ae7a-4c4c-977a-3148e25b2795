import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A reusable custom error dialog widget that follows the design pattern
/// of the existing connection_error_dialog.widget.dart
class CustomErrorDialog extends StatelessWidget {
  /// The error message to display (required)
  final String message;
  
  /// Path to the SVG icon (optional, defaults to access denied icon)
  final String iconPath;
  
  /// Color for the icon (optional, defaults to red)
  final Color iconColor;
  
  /// Dialog title (optional, defaults to "Erreur")
  final String title;
  
  /// Optional callback when the OK button is pressed
  final VoidCallback? onOkPressed;

  const CustomErrorDialog({
    super.key,
    required this.message,
    this.iconPath = 'assets/icons/icone_acces_refuse.svg',
    this.iconColor = Colors.red,
    this.title = 'Erreur',
    this.onOkPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      title: Text(
        title.toUpperCase(),
        textAlign: TextAlign.center,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            iconPath,
            width: 50,
            height: 50,
            colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
            ),
          ),
        ],
      ),
      actionsAlignment: MainAxisAlignment.center,
      actionsPadding: const EdgeInsets.all(15.0),
      actionsOverflowAlignment: OverflowBarAlignment.center,
      actions: [
        FilledButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
            minimumSize: WidgetStateProperty.all(const Size(150, 50)),
          ),
          onPressed: () {
            Navigator.of(context).pop();
            onOkPressed?.call();
          },
          child: const Text('OK'),
        ),
      ],
    );
  }

  /// Static method to show the dialog easily
  static Future<void> show(
    BuildContext context, {
    required String message,
    String iconPath = 'assets/icons/icone_acces_refuse.svg',
    Color iconColor = Colors.red,
    String title = 'Erreur',
    VoidCallback? onOkPressed,
    bool barrierDismissible = false,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext context) {
        return CustomErrorDialog(
          message: message,
          iconPath: iconPath,
          iconColor: iconColor,
          title: title,
          onOkPressed: onOkPressed,
        );
      },
    );
  }
}
