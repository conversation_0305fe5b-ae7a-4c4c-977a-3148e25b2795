import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ConnectionErrorDialog extends StatelessWidget {
  final VoidCallback? onRetry;

  const ConnectionErrorDialog({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      insetPadding: const EdgeInsets.symmetric(horizontal: 10),
      title: const Text(
        "ERREUR DE CONNEXION",
        textAlign: TextAlign.center,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            'assets/icons/icone_acces_refuse.svg',
            width: 50,
            height: 50,
            colorFilter: ColorFilter.mode(Colors.red, BlendMode.srcIn),
          ),
          const SizedBox(height: 16),
          Text.rich(
            TextSpan(
              children: [
                const TextSpan(
                  text: "Veuillez vérifier votre connexion internet\n",
                  style: TextStyle(
                    color: Colors.black,
                  ),
                ),
                TextSpan(
                  text: "L'application a des soucis pour accéder à internet",
                  style: TextStyle(
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
      actionsAlignment: MainAxisAlignment.center,
      actionsPadding: const EdgeInsets.all(15.0),
      actionsOverflowAlignment: OverflowBarAlignment.center,
      actions: [
        FilledButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
            minimumSize: WidgetStateProperty.all(const Size(150, 50)),
          ),
          onPressed: () {
            Navigator.of(context).pop();
            onRetry?.call();
          },
          child: const Text('RÉESSAYER'),
        ),
      ],
    );
  }
}
