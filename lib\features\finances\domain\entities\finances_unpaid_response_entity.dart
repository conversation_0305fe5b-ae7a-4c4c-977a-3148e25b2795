import 'package:equatable/equatable.dart';
import 'package:Kairos/features/finances/data/models/finances_unpaid_response_model.dart';
import 'package:Kairos/features/finances/domain/entities/frais_etudiant_entity.dart';

class FinancesUnpaidResponseEntity extends Equatable {
  final double? totalImpaye;
  final List<FraisEtudiantEntity>? fraisImPayes;

  const FinancesUnpaidResponseEntity({
    this.totalImpaye,
    this.fraisImPayes,
  });

  factory FinancesUnpaidResponseEntity.fromModel(FinancesUnpaidResponseModel model) {
    return FinancesUnpaidResponseEntity(
      totalImpaye: model.totalImpaye,
      fraisImPayes: model.fraisImPayes?.map<FraisEtudiantEntity>((m) => FraisEtudiantEntity.fromModel(m)).toList(),
    );
  }

  @override
  List<Object?> get props => [totalImpaye, fraisImPayes];
}
