import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/absence_retard_entity.dart';
import '../repositories/absences_retards_repository.dart';

/// Parameters for GetAbsencesRetardsFiltresUseCase
class GetAbsencesRetardsFiltresParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;
  final String? startDate;
  final String? endDate;

  const GetAbsencesRetardsFiltresParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
    this.startDate,
    this.endDate,
  });
}

/// Use case for getting filtered absences and tardiness records
class GetAbsencesRetardsFiltresUseCase implements UseCase<List<AbsenceRetardEntity>, GetAbsencesRetardsFiltresParams> {
  final AbsencesRetardsRepository repository;

  const GetAbsencesRetardsFiltresUseCase(this.repository);

  @override
  Future<Either<Failure, List<AbsenceRetardEntity>>> call(GetAbsencesRetardsFiltresParams params) async {
    return await repository.getAbsencesRetardsFiltres(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}
