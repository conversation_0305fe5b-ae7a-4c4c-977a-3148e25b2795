/// Simplified data model for school information
/// Represents the 'etablissement' object in the API response
/// This is a lighter version of the full Etablissement model
class SchoolModel {
  final String? id;
  final String libelleEtab;
  final String codeEtab;
  final String logoEtablissement;

  SchoolModel({
    this.id,
    required this.libelleEtab,
    required this.codeEtab,
    required this.logoEtablissement,
  });

  factory SchoolModel.fromJson(Map<String, dynamic> json) {
    return SchoolModel(
      id: json['id']??'',
      libelleEtab: json['libelleEtab'],
      codeEtab: json['codeEtab'],
      logoEtablissement: json['logoEtablissement'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'libelleEtab': libelleEtab,
      'codeEtab': codeEtab,
      'logoEtablissement': logoEtablissement,
    };
  }
}
