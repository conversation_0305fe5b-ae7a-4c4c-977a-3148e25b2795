import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/profile_entity.dart';
import '../../../authentication/data/models/deconnexion_request.dart';

/// Abstract repository for profile operations
abstract class ProfileRepository {
  /// Get user profile from local storage using codeUtilisateur
  Future<Either<Failure, ProfileEntity?>> getProfile(String codeUtilisateur);
  
  /// Logout user by calling suppressionEtablissement API
  Future<Either<Failure, void>> logout(DeconnexionRequest request);
  
  /// Remove user profile from local storage
  Future<Either<Failure, void>> removeProfile();
}
