﻿import 'package:equatable/equatable.dart';

/// Base notifications state
abstract class NotificationsState extends Equatable {
  const NotificationsState();
  
  @override
  List<Object?> get props => [];
}

/// Initial notifications state
class NotificationsInitial extends NotificationsState {
  const NotificationsInitial();
}

/// Loading state during notifications operations
class NotificationsLoading extends NotificationsState {
  const NotificationsLoading();
}

/// Notifications data loaded successfully
class NotificationsLoaded extends NotificationsState {
  final List<dynamic> data; // TODO: Replace with proper type
  
  const NotificationsLoaded({required this.data});
  
  @override
  List<Object?> get props => [data];
}

/// Notifications error occurred
class NotificationsError extends NotificationsState {
  final String message;
  
  const NotificationsError(this.message);
  
  @override
  List<Object?> get props => [message];
}
