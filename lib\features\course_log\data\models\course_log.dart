
class CourseLogEntry {
  final String day;
  final String date;
  final String timeRange;
  final String teacher;
  final String subject;
  final String registeredBy;
  final String registeredDate;
  final String courseCode;
  final String semester;
  final String duration;

  CourseLogEntry({
    required this.day,
    required this.date,
    required this.timeRange,
    required this.teacher,
    required this.subject,
    required this.registeredBy,
    required this.registeredDate,
    required this.courseCode,
    required this.semester,
    required this.duration,
  });
}