﻿import 'package:equatable/equatable.dart';

/// Base schedule state
abstract class ScheduleState extends Equatable {
  const ScheduleState();
  
  @override
  List<Object?> get props => [];
}

/// Initial schedule state
class ScheduleInitial extends ScheduleState {
  const ScheduleInitial();
}

/// Loading state during schedule operations
class ScheduleLoading extends ScheduleState {
  const ScheduleLoading();
}

/// Schedule data loaded successfully
class ScheduleLoaded extends ScheduleState {
  final List<dynamic> data; // TODO: Replace with proper type
  
  const ScheduleLoaded({required this.data});
  
  @override
  List<Object?> get props => [data];
}

/// Schedule error occurred
class ScheduleError extends ScheduleState {
  final String message;
  
  const ScheduleError(this.message);
  
  @override
  List<Object?> get props => [message];
}
