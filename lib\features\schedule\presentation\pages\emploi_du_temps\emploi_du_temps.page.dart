import 'package:Kairos/core/utils/date_utils.dart';
import 'package:Kairos/core/widgets/common/empty_message.widget.dart';
import 'package:flutter/material.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/features/schedule/presentation/pages/emploi_du_temps/schedule_item.widget.dart';
import 'package:Kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:Kairos/features/schedule/data/models/schedule_entry.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/widgets/inputs/search_bar_sliver.widget.dart';


class EmploiDuTempsPage extends StatefulWidget {
  const EmploiDuTempsPage({super.key});

  @override
  State<EmploiDuTempsPage> createState() => _EmploiDuTempsPageState();
}

class _EmploiDuTempsPageState extends State<EmploiDuTempsPage> with TickerProviderStateMixin {
  bool _isLoading = true;
  bool _isSearchBarVisible = false;
  late TextEditingController _searchController;
  late Map<String, List<ScheduleEntry>> _filteredScheduleEntriesGroupedByDay;
  late AnimationController _searchAnimationController;

  // Date filter state
  String? _startDateFilter;
  String? _endDateFilter;

  final Map<String, List<ScheduleEntry>> scheduleEntriesGroupedByDay = {
    'LUN': [
      ScheduleEntry(
        day: 'LUN',
        date: '12/12',
        timeRange: '08:00 - 10:30',
        courseCode: '1COM177',
        teacher: 'Avec Thiate BARRY',
        courseDetails: 'PALT_C1B_2021 | SEM1',
        location: 'BAT_A_2_1erETAGE',
        indicatorType: 'PL',
        indicatorColor: Colors.blue,
      ),
      ScheduleEntry(
        day: 'LUN',
        date: '12/12',
        timeRange: '11:00 - 01:00',
        courseCode: 'ALTC3S2N1',
        teacher: 'Avec Malick Y. FALL',
        courseDetails: 'PALT_C1B_2021 | SEM2',
        location: 'BAT_A_13_4eEtage',
        indicatorType: 'PE',
        indicatorColor: Colors.orange,
      ),
      ScheduleEntry(
        day: 'LUN',
        date: '12/12',
        timeRange: '01:30 - 03:30',
        courseCode: 'ALTC3S2N1',
        teacher: 'Avec Malick Y. FALL',
        courseDetails: 'PALT_C1B_2021 | SEM2',
        location: 'BAT_A_25_6eEtage',
        indicatorType: 'PT',
        indicatorColor: Colors.green,
      ),
    ],
    'MAR': [
      ScheduleEntry(
        day: 'MAR',
        date: '13/12',
        timeRange: '09:30 - 12:00',
        courseCode: 'ALTC3S2N1',
        teacher: 'Avec Malick Y. FALL',
        courseDetails: 'PALT_C1B_2021 | SEM2',
        location: 'BAT_A_14_4eEtage',
        indicatorType: 'PE',
        indicatorColor: Colors.orange,
      ),
      ScheduleEntry(
        day: 'MAR',
        date: '13/12',
        timeRange: '12:00 - 12:30',
        courseCode: '1COM177',
        teacher: 'Avec Thiate BARRY',
        courseDetails: 'PALT_C1B_2021 | SEM1',
        location: 'BAT_A_14_4eEtage',
        indicatorType: 'PT',
        indicatorColor: Colors.green,
      ),
    ],
    'MER': [
      ScheduleEntry(
        day: 'MER',
        date: '14/12',
        timeRange: '08:00 - 10:30',
        courseCode: 'ALTC2S1N1',
        teacher: 'Avec Malick Y. FALL',
        courseDetails: 'PALT_C1B_2021 | SEM1',
        location: 'BAT_A_18_5eEtage',
        indicatorType: 'PT',
        indicatorColor: Colors.green,
      ),
      ScheduleEntry(
        day: 'MER',
        date: '14/12',
        timeRange: '11:00 - 12:30',
        courseCode: 'ALTC2S1N1',
        teacher: 'Avec Malick Y. FALL',
        courseDetails: 'PALT_C1B_2021 | SEM1',
        location: 'BAT_A_12_3eETAGE',
        indicatorType: 'PL',
        indicatorColor: Colors.blue,
      ),
    ],
    'JEU': [
      ScheduleEntry(
        day: 'JEU',
        date: '15/12',
        timeRange: '07:00 - 09:30',
        courseCode: 'ALTC3S1N1',
        teacher: 'Avec Ismael NDIAYE',
        courseDetails: 'PALT_C1B_2021 | SEM1',
        location: 'BAT_A_13_4eEtage',
        indicatorType: 'PE',
        indicatorColor: Colors.orange,
      ),
    ],
    'VEN': [
      ScheduleEntry(
        day: 'VEN',
        date: '16/12',
        timeRange: '08:00 - 10:00',
        courseCode: 'ALTC3S2N1',
        teacher: 'Avec Malick Y. FALL',
        courseDetails: 'PALT_C1B_2021 | SEM2',
        location: 'BAT_A_13_4eEtage',
        indicatorType: 'PL',
        indicatorColor: Colors.blue,
      ),
    ],
  };

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _filteredScheduleEntriesGroupedByDay = Map.from(scheduleEntriesGroupedByDay);
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    _searchController.addListener(_filterScheduleEntries);

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  void _filterScheduleEntries() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty && _startDateFilter == null && _endDateFilter == null) {
        _filteredScheduleEntriesGroupedByDay = Map.from(scheduleEntriesGroupedByDay);
      } else {
        _filteredScheduleEntriesGroupedByDay.clear();
        for (final day in scheduleEntriesGroupedByDay.keys) {
          final entries = scheduleEntriesGroupedByDay[day]!;
          final filteredEntries = entries.where((entry) {
            // Text search filter
            bool matchesText = entry.courseCode.toLowerCase().contains(query) ||
                entry.teacher.toLowerCase().contains(query) ||
                entry.date.toLowerCase().contains(query);

            // Date range filter
            bool matchesDateRange = true;
            if (_startDateFilter != null && _endDateFilter != null) {
              matchesDateRange = isDateInRange(entry.date, _startDateFilter!, _endDateFilter!);
            }

            return matchesText && matchesDateRange;
          }).toList();
          if (filteredEntries.isNotEmpty) {
            _filteredScheduleEntriesGroupedByDay[day] = filteredEntries;
          }
        }
        debugPrint("Filtered entries: $_filteredScheduleEntriesGroupedByDay");
        debugPrint("is filteredEntries empty: ${_filteredScheduleEntriesGroupedByDay.isEmpty}");
      }
    });
  }

  // Method to handle date filter changes
  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDateFilter = dateRange['startDate'];
      _endDateFilter = dateRange['endDate'];
    });
    debugPrint("startDateFilter: $_startDateFilter, endDateFilter: $_endDateFilter");
    _filterScheduleEntries();
  }

  // Method to clear date filters
  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    // Reapply filters without date constraint
    _filterScheduleEntries();
  }

  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (_isSearchBarVisible) {
        _searchAnimationController.forward();
      } else {
        _searchAnimationController.reverse();
        _searchController.clear();
        _startDateFilter = null; // Clear date filters when hidden
        _endDateFilter = null;
        _filteredScheduleEntriesGroupedByDay = Map.from(scheduleEntriesGroupedByDay);
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: false,
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
            pageSection: HeaderEnum.planning,
            isSearchBarVisible: _isSearchBarVisible,
            title: "EMPLOI DU TEMPS",
            onSearchTap: _toggleSearchBarVisibility,
          ),
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                pinned: true,
                delegate: SearchBarSliver(
                  extentHeight: _searchAnimationController.value * 60.0, // Animate height
                  searchController: _searchController,
                  onSearchChanged: (query) => _filterScheduleEntries(),
                  onDateFilterChanged: _onDateFilterChanged,
                  onClearDateFilter: _clearDateFilter,
                  hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
                  hintText: "Rechercher un cours...",
                ),
              );
            },),
          _isLoading
          ? SliverFillRemaining(
              child: Center(
                child: CustomSpinner(
                  size: 60.0,
                  strokeWidth: 5.0,
                ),
              ),
            )
          : _filteredScheduleEntriesGroupedByDay.isEmpty
            ? SliverFillRemaining(
                child: Center(
                  child: EmptyMessage(message: "Aucun cours trouvé"),
                ),
              )
            :
          SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final dayEntries = _filteredScheduleEntriesGroupedByDay.entries.elementAt(index);
                  final day = dayEntries.key;
                  final entries = dayEntries.value;
                  final date = entries.isNotEmpty ? entries.first.date : '';

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 60,
                          margin: const EdgeInsets.only(right: 16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                day,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                date,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                          ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: entries.map((entry) => ScheduleItem(scheduleEntry: entry)).toList(),
                          ),
                        ),
                      ],
                    ),
                  );
                },
                childCount: _filteredScheduleEntriesGroupedByDay.length,
              ),
            ),
        ]
      )
    );
  }
}
