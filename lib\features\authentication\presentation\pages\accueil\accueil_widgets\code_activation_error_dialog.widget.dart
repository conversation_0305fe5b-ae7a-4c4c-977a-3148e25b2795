import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart'; // Import flutter_svg

class CodeActivationErrorDialog extends StatelessWidget {
  final int? returnCode;
  final String userMessage;
  final VoidCallback? onResend; // Callback for the "RENVOYER" button

  const CodeActivationErrorDialog({
    super.key,
    this.returnCode,
    required this.userMessage,
    this.onResend,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white, // Align with ConnectionErrorDialog
      insetPadding: const EdgeInsets.symmetric(horizontal: 10), // Add padding to the dialog
      title: const Text(
        'Erreur d\'activation', // Centered title
        textAlign: TextAlign.center,
      ),
      content: Column( // Wrap content in Column
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset( // Add SVG icon
            'assets/icons/icone_acces_refuse.svg',
            width: 50,
            height: 50,
            colorFilter: ColorFilter.mode(Colors.red, BlendMode.srcIn),
          ),
          const SizedBox(height: 16), // Add spacing
          Text( // User message text
            userMessage,
            textAlign: TextAlign.center, // Centered text
          ),
        ],
      ),
      actionsAlignment: MainAxisAlignment.center, // Center actions
      actionsPadding: const EdgeInsets.all(15.0), // Add actions padding
      actionsOverflowAlignment: OverflowBarAlignment.center, // Center overflow alignment
      actions: [
        if (returnCode == 4021) // Show "RENVOYER" button for returnCode 4021
          FilledButton( // Use FilledButton
            style: ButtonStyle( // Apply button style
              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
              minimumSize: WidgetStateProperty.all(const Size(150, 50)),
            ),
            onPressed: () {
              Navigator.of(context).pop(); // Dismiss dialog
              onResend?.call(); // Invoke the resend callback
            },
            child: const Text('RENVOYER'),
          )
        else // Show "OK" button for other errors
          FilledButton( // Use FilledButton
            style: ButtonStyle( // Apply button style
              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
              minimumSize: WidgetStateProperty.all(const Size(150, 50)),
            ),
            onPressed: () {
              Navigator.of(context).pop(); // Dismiss dialog
            },
            child: const Text('OK'),
          ),
      ],
    );
  }
}