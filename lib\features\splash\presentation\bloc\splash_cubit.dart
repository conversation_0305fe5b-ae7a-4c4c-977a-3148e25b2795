﻿import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'splash_state.dart';
import '../../../authentication/data/datasources/auth_local_datasource.dart';

/// Splash Cubit for managing splash state
class SplashCubit extends Cubit<SplashState> {
  final AuthLocalDataSource _authLocalDataSource;

  SplashCubit(this._authLocalDataSource) : super(const SplashInitial());

  /// Check authentication state and navigate accordingly
  ///
  /// This method checks if the user is authenticated by verifying:
  /// 1. Phone number exists and is not empty (excluding test number)
  /// 2. Authentication token exists and is not empty
  ///
  /// If both conditions are met, emits [SplashAuthenticated] to navigate to liste_etablissement
  /// Otherwise, emits [SplashUnauthenticated] to navigate to accueil page
  Future<void> checkAuthenticationState() async {
    emit(const SplashLoading());

    try {
      // Check if phone number exists in SharedPreferences
      final phoneNumber = await _authLocalDataSource.getPhoneNumber();

      // Check if authentication token exists in SharedPreferences
      final token = await _authLocalDataSource.getCachedToken();

      debugPrint('Authentication check - Phone: ${phoneNumber != null ? "exists" : "missing"}, Token: ${token != null ? "exists" : "missing"}');

      // Determine authentication state
      // Check if we have valid phone number (not the test number) and token
      final isValidPhoneNumber = phoneNumber != null &&
                                phoneNumber.isNotEmpty; // Exclude test number
      final isValidToken = token != null && token.isNotEmpty;

      if (isValidPhoneNumber && isValidToken) {
        // User is authenticated - both phone number and token are present and valid
        debugPrint('User is authenticated, navigating to liste_etablissement');
        emit(const SplashAuthenticated());
      } else {
        // User is not authenticated - missing phone number, token, or using test data
        debugPrint('User is not authenticated, navigating to accueil (Phone: $isValidPhoneNumber, Token: $isValidToken)');
        emit(const SplashUnauthenticated());
      }
    } catch (e) {
      debugPrint('Error checking authentication state: $e');
      // On error, default to unauthenticated state
      emit(const SplashUnauthenticated());
    }
  }

  /// Load splash data (legacy method - kept for compatibility)
  Future<void> loadSplashData() async {
    emit(const SplashLoading());

    try {
      // TODO: Implement load splash use case
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      // TODO: Replace with actual data
      emit(const SplashLoaded(data: []));
    } catch (e) {
      emit(SplashError(e.toString()));
    }
  }
  
  /// Refresh splash data
  Future<void> refresh() async {
    await loadSplashData();
  }
}
