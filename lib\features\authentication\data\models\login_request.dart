import 'dart:convert';

class LoginRequest {
  final String codeEtab;
  final String username;
  final String password;

  LoginRequest({
    required this.codeEtab,
    required this.username,
    required this.password,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) {
    return LoginRequest(
      codeEtab: json['codeEtab'],
      username: json['username'],
      password: json['password'],
    );
  }

  String toJson() => json.encode({
        'codeEtab': codeEtab,
        'username': username,
        'password': password,
      });
}
