import 'package:Kairos/core/services/device_info_service.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';
import '../../domain/entities/user.dart';
import '../../domain/entities/sms_response_entity.dart'; // Import the new entity
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_datasource.dart';
import '../datasources/auth_local_datasource.dart';
import '../models/sms_response_model.dart'; // Import the new model

/// Implementation of AuthRepository
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;

  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, User?>> login(String email, String password) async {
    try {
      final userModel = await remoteDataSource.login(email, password);
      await localDataSource.cacheUser(userModel);
      // return Right(userModel.toEntity());
      return Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }


  @override
  Future<Either<Failure, void>> logout() async {
    try {
      await remoteDataSource.logout();
      await localDataSource.clearCache();
      return const Right(null);
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> isAuthenticated() async {
    try {
      final isAuth = await localDataSource.isAuthenticated();
      return Right(isAuth);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, User?>> getCurrentUser() async {
    try {
      // final userModel = await localDataSource.getCachedUser();
      return Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, SmsResponseEntity>> sendSms(String phoneNumber) async { // Update return type
    try {
      final response = await remoteDataSource.sendSms(phoneNumber);
      // The remoteDataSource.sendSms already returns SmsResponseEntity
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> refreshToken() async {
    try {
      final token = await remoteDataSource.refreshToken();
      return Right(token);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> activateAccount(String activationCode) async {
    try {
      await remoteDataSource.activateAccount(activationCode);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, dynamic>> resendSms(String phoneNumber) async {
    try {
      final response = await remoteDataSource.resendSms(phoneNumber);

      // Assuming the response is a Map and contains device info
      if (response is Map<String, dynamic>) {
        final deviceInfo = {
          'marqueTelephone': response['marqueTelephone'],
          'modelTelephone': response['modelTelephone'],
          'imeiTelephone': response['imeiTelephone'],
          'numeroSerie': response['numeroSerie'],
        };
        await localDataSource.cacheDeviceInfo(deviceInfo);
      }

      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, dynamic>> verifyPinWithDetails(String fullName, String otp, String phoneNumber) async {
    try {
      // Retrieve device info from local data source
      dynamic deviceInfo = await localDataSource.getCachedDeviceInfo();

      // Handle case where device info is not found
      if (deviceInfo == null) {
        //get device infor froom DeviceInfoService
        deviceInfo = DeviceInfoService.deviceInfo;
        debugPrint("AuthRepositoryImpl: Device info not found locally. Using device info from DeviceInfoService: $deviceInfo");
        return Left(CacheFailure('Device information not found locally.'));
      }

      // Check if jwt_token exists in sharedPreferences
      final String? token = await localDataSource.getCachedToken();
      final bool tokenExists = token != null && token.isNotEmpty;

      // Call the remote data source method with tokenExists
      final response = await remoteDataSource.verifyPinWithDetails(
        fullName,
        otp,
        deviceInfo,
        phoneNumber,
        tokenExists: tokenExists,
      );
      debugPrint("AuthRepositoryImpl: verifyPinWithDetails response: $response");
      await localDataSource.cacheToken(response['token']);
      await localDataSource.saveFullName(fullName);
      await localDataSource.savePhoneNumber(phoneNumber);
      return Right(response);
    } on ServerException catch (e) {
      // Return ServerFailure with only the message
      return Left(ServerFailure(e.message, returnCode: e.returnCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, dynamic>> checkResponse(String phoneNumber, {required bool activated}) async {
    try {
      final response = await remoteDataSource.checkResponse(phoneNumber, activated: activated);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message, returnCode: e.returnCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }
}
