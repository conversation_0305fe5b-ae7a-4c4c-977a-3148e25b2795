﻿import 'package:equatable/equatable.dart';
import '../../domain/entities/enfant_tuteur_entity.dart';

/// Base student_records state
abstract class StudentRecordsState extends Equatable {
  const StudentRecordsState();

  @override
  List<Object?> get props => [];
}

/// Initial student_records state
class StudentRecordsInitial extends StudentRecordsState {
  const StudentRecordsInitial();
}

/// Loading state during student_records operations
class StudentRecordsLoading extends StudentRecordsState {
  const StudentRecordsLoading();
}

/// StudentRecords data loaded successfully
class StudentRecordsLoaded extends StudentRecordsState {
  final List<EnfantTuteurEntity> children;

  const StudentRecordsLoaded(this.children);

  @override
  List<Object?> get props => [children];
}

/// StudentRecords error occurred
class StudentRecordsError extends StudentRecordsState {
  final String message;

  const StudentRecordsError(this.message);

  @override
  List<Object?> get props => [message];
}
